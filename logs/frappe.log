2025-07-23 16:30:24,408 ERROR frappe Unable to load translations
Site: mis
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/caching.py", line 57, in wrapper
    return frappe.local.request_cache[func][args_key]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^
KeyError: 5962926092304900516

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/translate.py", line 174, in get_all_translations
    return frappe.cache.hget(MERGED_TRANSLATION_KEY, lang, generator=_merge_translations)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/redis_wrapper.py", line 241, in hget
    value = generator()
            ^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/translate.py", line 162, in _merge_translations
    all_translations.update(get_translations_from_apps(lang))
                            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/translate.py", line 191, in get_translations_from_apps
    for app in apps or frappe.get_installed_apps(_ensure_on_bench=True):
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/caching.py", line 59, in wrapper
    return_val = func(*args, **kwargs)
                 ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1553, in get_installed_apps
    installed = json.loads(db.get_global("installed_apps") or "[]")
                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/database/database.py", line 1134, in get_global
    return self.get_default(key, user)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/database/database.py", line 1138, in get_default
    d = self.get_defaults(key, parent)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/database/database.py", line 1154, in get_defaults
    defaults = frappe.defaults.get_defaults_for(parent)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/defaults.py", line 244, in get_defaults_for
    .run(as_dict=True)
     ^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/database/database.py", line 230, in sql
    self._cursor.execute(query, values)
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.ProgrammingError: (1146, "Table '_e16f6233902b64d0.tabDefaultValue' doesn't exist")
2025-07-23 16:30:24,416 ERROR frappe Unable to load translations
Site: mis
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/caching.py", line 57, in wrapper
    return frappe.local.request_cache[func][args_key]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^
KeyError: 5962926092304900516

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/translate.py", line 174, in get_all_translations
    return frappe.cache.hget(MERGED_TRANSLATION_KEY, lang, generator=_merge_translations)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/redis_wrapper.py", line 241, in hget
    value = generator()
            ^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/translate.py", line 162, in _merge_translations
    all_translations.update(get_translations_from_apps(lang))
                            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/translate.py", line 191, in get_translations_from_apps
    for app in apps or frappe.get_installed_apps(_ensure_on_bench=True):
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/caching.py", line 59, in wrapper
    return_val = func(*args, **kwargs)
                 ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1553, in get_installed_apps
    installed = json.loads(db.get_global("installed_apps") or "[]")
                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/database/database.py", line 1134, in get_global
    return self.get_default(key, user)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/database/database.py", line 1138, in get_default
    d = self.get_defaults(key, parent)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/database/database.py", line 1154, in get_defaults
    defaults = frappe.defaults.get_defaults_for(parent)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/defaults.py", line 244, in get_defaults_for
    .run(as_dict=True)
     ^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/database/database.py", line 230, in sql
    self._cursor.execute(query, values)
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.ProgrammingError: (1146, "Table '_e16f6233902b64d0.tabDefaultValue' doesn't exist")
2025-07-24 14:10:56,962 ERROR frappe New Exception collected in error log
Site: neel
Form Dict: {'report_name': 'Workflow Turnaround Analysis Report', 'filters': '{"from_date":"2025-06-24","to_date":"2025-07-24"}', 'file_format_type': 'Excel', 'cmd': 'frappe.desk.query_report.export_query'}
2025-07-24 14:15:44,243 ERROR frappe New Exception collected in error log
Site: neel
Form Dict: {'report_name': 'Workflow Turnaround Analysis Report', 'filters': '{"from_date":"2025-06-24","to_date":"2025-07-24","report_type":"Workflow Bottleneck Analysis"}', 'ignore_prepared_report': 'false', 'are_default_filters': 'false', 'cmd': 'frappe.desk.query_report.run'}
2025-07-24 14:16:04,202 ERROR frappe New Exception collected in error log
Site: neel
Form Dict: {'report_name': 'Workflow Turnaround Analysis Report', 'filters': '{"from_date":"2025-06-24","to_date":"2025-07-24","report_type":"Workflow Bottleneck Analysis"}', 'ignore_prepared_report': 'false', 'are_default_filters': 'false', 'cmd': 'frappe.desk.query_report.run'}
2025-07-24 14:25:15,569 ERROR frappe New Exception collected in error log
Site: neel
Form Dict: {'report_name': 'Workflow Turnaround Analysis Report', 'filters': '{"from_date":"2025-06-24","to_date":"2025-07-24","report_type":"Workflow Bottleneck Analysis"}', 'ignore_prepared_report': 'false', 'are_default_filters': 'false', 'cmd': 'frappe.desk.query_report.run'}
2025-07-24 14:26:02,933 ERROR frappe New Exception collected in error log
Site: neel
Form Dict: {'report_name': 'Workflow Turnaround Analysis Report', 'filters': '{"from_date":"2025-06-24","to_date":"2025-07-24","report_type":"Workflow Bottleneck Analysis"}', 'ignore_prepared_report': 'false', 'are_default_filters': 'false', 'cmd': 'frappe.desk.query_report.run'}
2025-07-24 14:41:12,851 ERROR frappe New Exception collected in error log
Site: neel
Form Dict: {'report_name': 'Workflow Turnaround Analysis Report', 'filters': '{"from_date":"2025-06-24","to_date":"2025-07-24","report_type":"Workflow Bottleneck Analysis"}', 'ignore_prepared_report': 'false', 'are_default_filters': 'false', 'cmd': 'frappe.desk.query_report.run'}
2025-07-24 14:41:34,812 ERROR frappe New Exception collected in error log
Site: neel
Form Dict: {'report_name': 'Workflow Turnaround Analysis Report', 'filters': '{"from_date":"2025-06-24","to_date":"2025-07-24","report_type":"Workflow Bottleneck Analysis"}', 'ignore_prepared_report': 'false', 'are_default_filters': 'false', 'cmd': 'frappe.desk.query_report.run'}
2025-07-24 14:41:35,168 ERROR frappe New Exception collected in error log
Site: neel
Form Dict: {'report_name': 'Workflow Turnaround Analysis Report', 'filters': '{"from_date":"2025-06-24","to_date":"2025-07-24","report_type":"Workflow Bottleneck Analysis"}', 'ignore_prepared_report': 'false', 'are_default_filters': 'false', 'cmd': 'frappe.desk.query_report.run'}
2025-07-24 16:40:13,597 ERROR frappe New Exception collected in error log
Site: clearing
Form Dict: {'customer': '', 'customer_phone': '', 'delivery_note': '', 'delivery_person': '', 'received_date': '', 'branch_agent': '', 'branch_agent_name': '', 'destination': '', 'total_amount': 0, 'goods_details': [], 'main_agent': '<EMAIL>', 'cmd': 'tenaciousfreightmaster.tenacious_freightmaster.api.create_goods_receipt'}
2025-07-24 16:40:45,384 ERROR frappe New Exception collected in error log
Site: clearing
Form Dict: {'customer': '', 'customer_phone': '', 'delivery_note': '', 'delivery_person': '', 'received_date': '', 'branch_agent': '', 'branch_agent_name': '', 'destination': '', 'total_amount': 0, 'goods_details': [], 'main_agent': '<EMAIL>', 'cmd': 'tenaciousfreightmaster.tenacious_freightmaster.api.create_goods_receipt'}
2025-07-24 16:52:27,911 ERROR frappe New Exception collected in error log
Site: clearing
Form Dict: {'doc_name': 'GR-2025-07-00156', 'cmd': 'tenaciousfreightmaster.tenacious_freightmaster.doctype.goods_receipt.goods_receipt.create_delivery_note'}
2025-07-25 10:17:04,941 ERROR frappe New Exception collected in error log
Site: helpdesk
Form Dict: {}
2025-07-25 10:20:26,343 ERROR frappe New Exception collected in error log
Site: thps.or.tz
Form Dict: {'app_path': 'tickets/new'}
2025-07-25 10:42:13,630 ERROR frappe New Exception collected in error log
Site: thps.or.tz
Form Dict: {'with_parent': '1', 'cached_timestamp': '', 'cmd': 'frappe.desk.form.load.getdoctype'}
