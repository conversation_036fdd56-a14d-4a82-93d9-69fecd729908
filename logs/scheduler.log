2025-07-23 16:48:37,449 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_monthly because it was found in queue for integration
2025-07-23 16:48:37,454 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for integration
2025-07-23 16:48:37,455 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.weekly_backup because it was found in queue for integration
2025-07-23 16:48:37,459 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for integration
2025-07-23 16:48:37,466 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_weekly because it was found in queue for integration
2025-07-24 09:47:38,794 ERROR scheduler Skipped queueing frappe.utils.change_log.check_for_update because it was found in queue for integration
2025-07-24 09:47:38,808 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_weekly because it was found in queue for integration
2025-07-24 09:47:38,820 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for integration
2025-07-24 09:47:38,821 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for integration
2025-07-24 09:47:38,824 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for integration
2025-07-24 09:47:38,832 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_monthly because it was found in queue for integration
2025-07-24 09:47:38,838 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.weekly_backup because it was found in queue for integration
2025-07-24 09:47:38,840 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for integration
2025-07-24 09:47:38,841 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for integration
2025-07-24 09:47:38,850 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_weekly_updates because it was found in queue for integration
2025-07-24 09:47:38,855 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for integration
2025-07-24 09:47:38,856 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_weekly because it was found in queue for integration
2025-07-24 09:47:38,868 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for certifications
2025-07-24 09:47:38,871 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for certifications
2025-07-24 09:47:38,875 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for certifications
2025-07-24 09:47:38,879 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for certifications
2025-07-24 09:47:38,880 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for certifications
2025-07-24 09:47:38,882 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for certifications
2025-07-24 09:47:38,886 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for certifications
2025-07-24 09:47:38,890 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for certifications
2025-07-24 09:47:38,892 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for certifications
2025-07-24 09:47:38,893 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for certifications
2025-07-24 09:47:38,894 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for certifications
2025-07-24 09:47:38,895 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for certifications
2025-07-24 09:47:38,897 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for certifications
2025-07-24 09:47:38,899 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for certifications
2025-07-24 09:47:38,901 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for certifications
2025-07-24 09:47:38,902 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for certifications
2025-07-24 09:47:38,904 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for certifications
2025-07-24 09:47:38,905 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for certifications
2025-07-24 09:47:38,907 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for certifications
2025-07-24 09:47:38,923 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for wasco
2025-07-24 09:47:38,931 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for wasco
2025-07-24 09:47:38,935 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for wasco
2025-07-24 09:47:38,940 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_assignment_schedule.shift_assignment_schedule.process_auto_shift_creation because it was found in queue for wasco
2025-07-24 09:47:38,942 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for wasco
2025-07-24 09:47:38,952 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for wasco
2025-07-24 09:47:38,963 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for wasco
2025-07-24 09:47:38,966 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for wasco
2025-07-24 09:47:38,984 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for wasco
2025-07-24 09:47:38,985 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for wasco
2025-07-24 09:47:38,992 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for wasco
2025-07-24 09:47:38,997 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for wasco
2025-07-24 09:47:39,001 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for wasco
2025-07-24 09:47:39,006 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for wasco
2025-07-24 09:47:39,007 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for wasco
2025-07-24 09:47:39,012 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for wasco
2025-07-24 09:47:39,032 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for attendance
2025-07-24 09:47:39,046 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for attendance
2025-07-24 09:47:39,056 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for attendance
2025-07-24 09:47:39,059 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for attendance
2025-07-24 09:47:39,063 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for attendance
2025-07-24 09:47:39,064 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for attendance
2025-07-24 09:47:39,066 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for attendance
2025-07-24 09:47:39,068 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for attendance
2025-07-24 09:47:39,075 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for attendance
2025-07-24 09:47:39,076 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for attendance
2025-07-24 09:47:39,080 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for attendance
2025-07-24 09:47:39,082 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for attendance
2025-07-24 09:47:39,086 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for attendance
2025-07-24 09:47:39,090 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for attendance
2025-07-24 09:47:39,092 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for attendance
2025-07-24 09:47:39,098 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for attendance
2025-07-24 09:47:39,108 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for attendance
2025-07-24 09:47:39,120 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for attendance
2025-07-24 09:47:39,122 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for attendance
2025-07-24 09:47:39,123 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for attendance
2025-07-24 09:47:39,128 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for attendance
2025-07-24 09:47:39,129 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for attendance
2025-07-24 09:47:39,131 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for attendance
2025-07-24 09:47:39,133 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for attendance
2025-07-24 09:47:39,139 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for attendance
2025-07-24 09:47:39,143 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for attendance
2025-07-24 09:47:39,146 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for attendance
2025-07-24 09:47:39,149 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for attendance
2025-07-24 09:47:39,152 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for attendance
2025-07-24 09:47:39,155 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for attendance
2025-07-24 09:47:39,157 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for attendance
2025-07-24 09:47:39,158 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for attendance
2025-07-24 09:47:39,171 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for beetle
2025-07-24 09:47:39,173 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for beetle
2025-07-24 09:47:39,175 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for beetle
2025-07-24 09:47:39,179 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for beetle
2025-07-24 09:47:39,181 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for beetle
2025-07-24 09:47:39,183 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for beetle
2025-07-24 09:47:39,186 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for beetle
2025-07-24 09:47:39,188 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for beetle
2025-07-24 09:47:39,189 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for beetle
2025-07-24 09:47:39,196 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for beetle
2025-07-24 09:47:39,197 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for beetle
2025-07-24 09:47:39,200 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for beetle
2025-07-24 09:47:39,209 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for beetle
2025-07-24 09:47:39,210 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for beetle
2025-07-24 09:47:39,221 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for beetle
2025-07-24 09:47:39,225 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for beetle
2025-07-24 09:47:39,227 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for beetle
2025-07-24 09:47:39,232 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for beetle
2025-07-24 09:47:39,236 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for beetle
2025-07-24 09:47:39,240 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for beetle
2025-07-24 09:47:39,247 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for beetle
2025-07-24 09:47:39,249 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for beetle
2025-07-24 09:47:39,251 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for beetle
2025-07-24 09:47:39,253 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for beetle
2025-07-24 09:47:39,255 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for beetle
2025-07-24 09:47:39,258 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for beetle
2025-07-24 09:47:39,263 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for beetle
2025-07-24 09:47:39,265 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for beetle
2025-07-24 09:47:39,266 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for beetle
2025-07-25 10:19:38,491 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for wasco
2025-07-25 10:19:38,515 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for wasco
2025-07-25 10:19:38,525 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for wasco
2025-07-25 10:19:38,527 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for wasco
2025-07-25 10:19:38,530 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for wasco
2025-07-25 10:19:38,535 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for wasco
2025-07-25 10:19:38,544 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for wasco
2025-07-25 10:19:38,547 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for wasco
2025-07-25 10:19:38,549 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for wasco
2025-07-25 10:19:38,552 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for wasco
2025-07-25 10:19:38,554 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for wasco
2025-07-25 10:19:38,556 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_assignment_schedule.shift_assignment_schedule.process_auto_shift_creation because it was found in queue for wasco
2025-07-25 10:19:38,576 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for wasco
2025-07-25 10:19:38,580 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for wasco
2025-07-25 10:19:38,582 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for wasco
2025-07-25 10:19:38,584 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for wasco
2025-07-25 10:19:38,597 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for attendance
2025-07-25 10:19:38,600 ERROR scheduler Skipped queueing hrms.controllers.employee_reminders.send_work_anniversary_reminders because it was found in queue for attendance
2025-07-25 10:19:38,601 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for attendance
2025-07-25 10:19:38,602 ERROR scheduler Skipped queueing frappe.desk.doctype.event.event.send_event_digest because it was found in queue for attendance
2025-07-25 10:19:38,604 ERROR scheduler Skipped queueing erpnext.accounts.utils.run_ledger_health_checks because it was found in queue for attendance
2025-07-25 10:19:38,605 ERROR scheduler Skipped queueing erpnext.quality_management.doctype.quality_review.quality_review.review because it was found in queue for attendance
2025-07-25 10:19:38,607 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for attendance
2025-07-25 10:19:38,608 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for attendance
2025-07-25 10:19:38,610 ERROR scheduler Skipped queueing erpnext.crm.doctype.opportunity.opportunity.auto_close_opportunity because it was found in queue for attendance
2025-07-25 10:19:38,611 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for attendance
2025-07-25 10:19:38,612 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for attendance
2025-07-25 10:19:38,615 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty because it was found in queue for attendance
2025-07-25 10:19:38,618 ERROR scheduler Skipped queueing erpnext.support.doctype.issue.issue.auto_close_tickets because it was found in queue for attendance
2025-07-25 10:19:38,619 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for attendance
2025-07-25 10:19:38,621 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for attendance
2025-07-25 10:19:38,623 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for attendance
2025-07-25 10:19:38,625 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for attendance
2025-07-25 10:19:38,626 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for attendance
2025-07-25 10:19:38,628 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for attendance
2025-07-25 10:19:38,629 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for attendance
2025-07-25 10:19:38,631 ERROR scheduler Skipped queueing erpnext.accounts.doctype.gl_entry.gl_entry.rename_gle_sle_docs because it was found in queue for attendance
2025-07-25 10:19:38,632 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for attendance
2025-07-25 10:19:38,639 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for attendance
2025-07-25 10:19:38,641 ERROR scheduler Skipped queueing erpnext.controllers.accounts_controller.update_invoice_status because it was found in queue for attendance
2025-07-25 10:19:38,642 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_daily_updates because it was found in queue for attendance
2025-07-25 10:19:38,643 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for attendance
2025-07-25 10:19:38,644 ERROR scheduler Skipped queueing biometric_client.biometric_client.doctype.biometric_data_staging.biometric_data_staging.process_biometric_logs because it was found in queue for attendance
2025-07-25 10:19:38,646 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.asset.update_maintenance_status because it was found in queue for attendance
2025-07-25 10:19:38,648 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.vehicle_fine_record.vehicle_fine_record.check_fine_all_vehicles because it was found in queue for attendance
2025-07-25 10:19:38,649 ERROR scheduler Skipped queueing frappe.email.doctype.notification.notification.trigger_daily_alerts because it was found in queue for attendance
2025-07-25 10:19:38,650 ERROR scheduler Skipped queueing biometric_client.biometric_client.doctype.biometric_data_staging.biometric_data_staging.send_exceptional_report because it was found in queue for attendance
2025-07-25 10:19:38,652 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset_maintenance_log.asset_maintenance_log.update_asset_maintenance_log_status because it was found in queue for attendance
2025-07-25 10:19:38,653 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for attendance
2025-07-25 10:19:38,655 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for attendance
2025-07-25 10:19:38,656 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for attendance
2025-07-25 10:19:38,657 ERROR scheduler Skipped queueing erpnext.buying.doctype.supplier_scorecard.supplier_scorecard.refresh_scorecards because it was found in queue for attendance
2025-07-25 10:19:38,658 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for attendance
2025-07-25 10:19:38,660 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for attendance
2025-07-25 10:19:38,661 ERROR scheduler Skipped queueing erpnext.projects.doctype.task.task.set_tasks_as_overdue because it was found in queue for attendance
2025-07-25 10:19:38,663 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for attendance
2025-07-25 10:19:38,664 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.update_project_sales_billing because it was found in queue for attendance
2025-07-25 10:19:38,666 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.asset.make_post_gl_entry because it was found in queue for attendance
2025-07-25 10:19:38,668 ERROR scheduler Skipped queueing csf_tz.bank_api.reconciliation because it was found in queue for attendance
2025-07-25 10:19:38,670 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for attendance
2025-07-25 10:19:38,671 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for attendance
2025-07-25 10:19:38,672 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_daily_feedback_reminder because it was found in queue for attendance
2025-07-25 10:19:38,674 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for attendance
2025-07-25 10:19:38,675 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for attendance
2025-07-25 10:19:38,676 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for attendance
2025-07-25 10:19:38,678 ERROR scheduler Skipped queueing hrms.hr.doctype.job_opening.job_opening.close_expired_job_openings because it was found in queue for attendance
2025-07-25 10:19:38,679 ERROR scheduler Skipped queueing erpnext.accounts.utils.auto_create_exchange_rate_revaluation_daily because it was found in queue for attendance
2025-07-25 10:19:38,681 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for attendance
2025-07-25 10:19:38,683 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for attendance
2025-07-25 10:19:38,684 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.parking_bill.parking_bill.check_bills_all_vehicles because it was found in queue for attendance
2025-07-25 10:19:38,685 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for attendance
2025-07-25 10:19:38,686 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for attendance
2025-07-25 10:19:38,688 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for attendance
2025-07-25 10:19:38,689 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for attendance
2025-07-25 10:19:38,690 ERROR scheduler Skipped queueing erpnext.buying.doctype.supplier_quotation.supplier_quotation.set_expired_status because it was found in queue for attendance
2025-07-25 10:19:38,691 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for attendance
2025-07-25 10:19:38,692 ERROR scheduler Skipped queueing erpnext.selling.doctype.quotation.quotation.set_expired_status because it was found in queue for attendance
2025-07-25 10:19:38,693 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for attendance
2025-07-25 10:19:38,694 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.hourly_reminder because it was found in queue for attendance
2025-07-25 10:19:38,696 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for attendance
2025-07-25 10:19:38,697 ERROR scheduler Skipped queueing erpnext.support.doctype.service_level_agreement.service_level_agreement.check_agreement_status because it was found in queue for attendance
2025-07-25 10:19:38,698 ERROR scheduler Skipped queueing csf_tz.custom_api.auto_close_dn because it was found in queue for attendance
2025-07-25 10:19:38,699 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for attendance
2025-07-25 10:19:38,700 ERROR scheduler Skipped queueing csf_tz.custom_api.create_delivery_note_for_all_pending_sales_invoice because it was found in queue for attendance
2025-07-25 10:19:38,701 ERROR scheduler Skipped queueing erpnext.accounts.doctype.fiscal_year.fiscal_year.auto_create_fiscal_year because it was found in queue for attendance
2025-07-25 10:19:38,702 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for attendance
2025-07-25 10:19:38,703 ERROR scheduler Skipped queueing erpnext.crm.doctype.contract.contract.update_status_for_contracts because it was found in queue for attendance
2025-07-25 10:19:38,705 ERROR scheduler Skipped queueing erpnext.setup.doctype.company.company.cache_companies_monthly_sales_history because it was found in queue for attendance
2025-07-25 10:19:38,706 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for attendance
2025-07-25 10:19:38,709 ERROR scheduler Skipped queueing erpnext.crm.doctype.email_campaign.email_campaign.set_email_campaign_status because it was found in queue for attendance
2025-07-25 10:19:38,710 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.project_status_update_reminder because it was found in queue for attendance
2025-07-25 10:19:38,712 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for attendance
2025-07-25 10:19:38,714 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_statement_of_accounts.process_statement_of_accounts.send_auto_email because it was found in queue for attendance
2025-07-25 10:19:38,716 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for attendance
2025-07-25 10:19:38,717 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.send_summary because it was found in queue for attendance
2025-07-25 10:19:38,718 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for attendance
2025-07-25 10:19:38,720 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for attendance
2025-07-25 10:19:38,722 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for attendance
2025-07-25 10:19:38,723 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.send_project_status_email_to_users because it was found in queue for attendance
2025-07-25 10:19:38,725 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.material_request.auto_close_material_request because it was found in queue for attendance
2025-07-25 10:19:38,727 ERROR scheduler Skipped queueing erpnext.stock.reorder_item.reorder_item because it was found in queue for attendance
2025-07-25 10:19:38,728 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for attendance
2025-07-25 10:19:38,738 ERROR scheduler Skipped queueing lending.loan_management.doctype.process_loan_interest_accrual.process_loan_interest_accrual.process_loan_interest_accrual_for_term_loans because it was found in queue for thps.or.tz
2025-07-25 10:19:38,758 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for thps.or.tz
2025-07-25 10:19:38,766 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for thps.or.tz
2025-07-25 10:19:38,779 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for thps.or.tz
2025-07-25 10:19:38,782 ERROR scheduler Skipped queueing lending.loan_management.doctype.process_loan_classification.process_loan_classification.create_process_loan_classification because it was found in queue for thps.or.tz
2025-07-25 10:19:38,784 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for thps.or.tz
2025-07-25 10:19:38,786 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for thps.or.tz
2025-07-25 10:19:38,792 ERROR scheduler Skipped queueing lending.loan_management.doctype.process_loan_security_shortfall.process_loan_security_shortfall.create_process_loan_security_shortfall because it was found in queue for thps.or.tz
2025-07-25 10:19:38,797 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for thps.or.tz
2025-07-25 10:19:38,802 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for thps.or.tz
2025-07-25 10:19:38,809 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for thps.or.tz
2025-07-25 10:19:38,813 ERROR scheduler Skipped queueing frappe_whatsapp.utils.trigger_whatsapp_notifications_hourly_long because it was found in queue for thps.or.tz
2025-07-25 10:19:38,814 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for thps.or.tz
2025-07-25 10:19:38,818 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for thps.or.tz
2025-07-25 10:19:38,820 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for thps.or.tz
2025-07-25 10:19:38,825 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for thps.or.tz
2025-07-25 10:19:38,840 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for thps.or.tz
2025-07-25 10:19:38,842 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for thps.or.tz
2025-07-25 10:19:38,846 ERROR scheduler Skipped queueing frappe_whatsapp.utils.trigger_whatsapp_notifications_daily_long because it was found in queue for thps.or.tz
2025-07-25 10:19:38,848 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for thps.or.tz
2025-07-25 10:19:38,850 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_assignment_schedule.shift_assignment_schedule.process_auto_shift_creation because it was found in queue for thps.or.tz
2025-07-25 10:19:38,865 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for beetle
2025-07-25 10:19:38,877 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for beetle
2025-07-25 10:19:38,881 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for beetle
2025-07-25 10:19:38,883 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for beetle
2025-07-25 10:19:38,885 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for beetle
2025-07-25 10:19:38,889 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for beetle
2025-07-25 10:19:38,892 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for beetle
2025-07-25 10:19:38,894 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for beetle
2025-07-25 10:19:38,896 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for beetle
2025-07-25 10:19:38,899 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for beetle
2025-07-25 10:19:38,905 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for beetle
2025-07-25 10:19:38,908 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for beetle
2025-07-25 10:19:38,916 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for beetle
2025-07-25 10:19:38,917 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for beetle
2025-07-25 10:19:38,918 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for beetle
2025-07-25 10:19:38,921 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for beetle
2025-07-25 10:19:38,922 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for beetle
2025-07-25 10:19:38,923 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for beetle
2025-07-25 10:19:38,927 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for beetle
2025-07-25 10:19:38,929 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for beetle
2025-07-25 10:19:38,934 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for beetle
2025-07-25 10:19:38,936 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for beetle
2025-07-25 10:19:38,937 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for beetle
2025-07-25 10:19:38,940 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for beetle
2025-07-25 10:19:38,971 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for beetle
2025-07-25 10:19:38,974 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for beetle
2025-07-25 10:19:38,978 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for beetle
2025-07-25 10:19:38,984 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for beetle
2025-07-25 10:19:38,985 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for beetle
2025-07-25 10:19:38,987 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for beetle
2025-07-25 10:19:38,991 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for beetle
2025-07-25 10:19:39,001 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for certifications
2025-07-25 10:19:39,013 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for certifications
2025-07-25 10:19:39,015 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for certifications
2025-07-25 10:19:39,021 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for certifications
2025-07-25 10:19:39,025 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for certifications
2025-07-25 10:19:39,026 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for certifications
2025-07-25 10:19:39,028 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for certifications
2025-07-25 10:19:39,030 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for certifications
2025-07-25 10:19:39,034 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for certifications
2025-07-25 10:19:39,036 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for certifications
2025-07-25 10:19:39,038 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for certifications
2025-07-25 10:19:39,040 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for certifications
2025-07-25 10:19:39,041 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for certifications
2025-07-25 10:19:39,043 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for certifications
2025-07-25 10:19:39,045 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for certifications
2025-07-25 10:19:39,046 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for certifications
2025-07-25 10:19:39,048 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for certifications
2025-07-25 10:19:39,051 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for certifications
2025-07-25 10:19:39,052 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for certifications
2025-07-25 10:19:39,060 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.weekly_backup because it was found in queue for integration
2025-07-25 10:19:39,065 ERROR scheduler Skipped queueing frappe.utils.change_log.check_for_update because it was found in queue for integration
2025-07-25 10:19:39,066 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for integration
2025-07-25 10:19:39,077 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for integration
2025-07-25 10:19:39,085 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for integration
2025-07-25 10:19:39,088 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for integration
2025-07-25 10:19:39,089 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for integration
2025-07-25 10:19:39,094 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_weekly_updates because it was found in queue for integration
2025-07-25 10:19:39,098 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_weekly because it was found in queue for integration
2025-07-25 10:19:39,101 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for integration
2025-07-25 10:19:39,110 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_monthly because it was found in queue for integration
2025-07-25 10:19:39,121 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_weekly because it was found in queue for integration
2025-07-25 10:21:39,336 ERROR scheduler Exception in Enqueue Events for Site libms
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/caching.py", line 57, in wrapper
    return frappe.local.request_cache[func][args_key]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^
KeyError: 5740354900026072187

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 98, in enqueue_events_for_site
    frappe.connect()
  File "/home/<USER>/Desktop/frappe-bench/apps/csf_tz/csf_tz/__init__.py", line 56, in connect
    load_monkey_patches()
  File "/home/<USER>/Desktop/frappe-bench/apps/csf_tz/csf_tz/__init__.py", line 26, in load_monkey_patches
    if app_name not in frappe.get_installed_apps():
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/caching.py", line 59, in wrapper
    return_val = func(*args, **kwargs)
                 ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1553, in get_installed_apps
    installed = json.loads(db.get_global("installed_apps") or "[]")
                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/database/database.py", line 1134, in get_global
    return self.get_default(key, user)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/database/database.py", line 1138, in get_default
    d = self.get_defaults(key, parent)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/database/database.py", line 1154, in get_defaults
    defaults = frappe.defaults.get_defaults_for(parent)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/defaults.py", line 244, in get_defaults_for
    .run(as_dict=True)
     ^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
                                                             ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/database/mariadb/database.py", line 108, in get_connection
    conn = self._get_connection()
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/database/mariadb/database.py", line 114, in _get_connection
    return self.create_connection()
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/database/mariadb/database.py", line 117, in create_connection
    return pymysql.connect(**self.get_connection_settings())
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
                  ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1045, "Access denied for user '_a6fa96726da8f0b5'@'localhost' (using password: YES)")
