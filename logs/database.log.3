2025-07-15 15:18:01,443 WARNING database DDL Query made to DB:
create table `tabKanban Board` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`kanban_board_name` varchar(140) unique,
`reference_doctype` varchar(140),
`field_name` varchar(140),
`private` int(1) not null default 0,
`show_labels` int(1) not null default 0,
`filters` longtext,
`fields` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 15:18:01,584 WARNING database DDL Query made to DB:
create table `tabGlobal Search DocType` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`document_type` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 15:18:01,678 WARNING database DDL Query made to DB:
create table `tabEvent Participants` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`reference_doctype` varchar(140),
`reference_docname` varchar(140),
`email` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 15:18:01,804 WARNING database DDL Query made to DB:
create table `tabDashboard Chart Field` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`y_field` varchar(140),
`color` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 15:18:01,959 WARNING database DDL Query made to DB:
create table `tabCustom HTML Block` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`private` int(1) not null default 0,
`html` longtext,
`script` longtext,
`style` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 15:18:02,051 WARNING database DDL Query made to DB:
create table `tabDashboard Settings` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`user` varchar(140),
`chart_config` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 15:18:02,388 WARNING database DDL Query made to DB:
create table `tabCalendar View` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`reference_doctype` varchar(140),
`subject_field` varchar(140),
`start_date_field` varchar(140),
`end_date_field` varchar(140),
`all_day` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 15:18:02,469 WARNING database DDL Query made to DB:
create table `tabTag` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`description` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 15:18:02,623 WARNING database DDL Query made to DB:
create table `tabDesktop Icon` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`module_name` varchar(140),
`label` varchar(140),
`standard` int(1) not null default 0,
`custom` int(1) not null default 0,
`app` varchar(140),
`description` text,
`category` varchar(140),
`hidden` int(1) not null default 0,
`blocked` int(1) not null default 0,
`force_show` int(1) not null default 0,
`type` varchar(140),
`_doctype` varchar(140),
`_report` varchar(140),
`link` text,
`color` varchar(140),
`icon` varchar(140),
`reverse` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 15:18:02,686 WARNING database DDL Query made to DB:
alter table `tabDesktop Icon`
					add unique `unique_module_name_owner_standard`(module_name, owner, standard)
2025-07-15 15:18:02,784 WARNING database DDL Query made to DB:
create table `tabTag Link` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`document_type` varchar(140),
`document_name` varchar(140),
`tag` varchar(140),
`title` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 15:18:02,851 WARNING database DDL Query made to DB:
ALTER TABLE `tabTag Link`
				ADD INDEX IF NOT EXISTS `document_type_document_name_index`(document_type, document_name)
2025-07-15 15:18:02,957 WARNING database DDL Query made to DB:
create table `tabChangelog Feed` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140),
`app_name` varchar(140),
`link` longtext,
`posting_timestamp` datetime(6),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `posting_timestamp`(`posting_timestamp`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 15:18:03,037 WARNING database DDL Query made to DB:
create table `tabNotification Settings` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`enabled` int(1) not null default 1,
`enable_email_notifications` int(1) not null default 1,
`enable_email_mention` int(1) not null default 1,
`enable_email_assignment` int(1) not null default 1,
`enable_email_threads_on_assigned_document` int(1) not null default 1,
`enable_email_energy_point` int(1) not null default 1,
`enable_email_share` int(1) not null default 1,
`enable_email_event_reminders` int(1) not null default 1,
`user` varchar(140),
`seen` int(1) not null default 0,
`energy_points_system_notifications` int(1) not null default 1,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 15:18:03,130 WARNING database DDL Query made to DB:
create table `tabNotification Log` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`subject` text,
`for_user` varchar(140),
`type` varchar(140),
`email_content` longtext,
`document_type` varchar(140),
`read` int(1) not null default 0,
`document_name` varchar(140),
`attached_file` longtext,
`from_user` varchar(140),
`link` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
`_seen` text,
index `for_user`(`for_user`),
index `document_name`(`document_name`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 15:18:03,277 WARNING database DDL Query made to DB:
create table `tabList View Settings` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`disable_count` int(1) not null default 0,
`disable_comment_count` int(1) not null default 0,
`disable_sidebar_stats` int(1) not null default 0,
`disable_auto_refresh` int(1) not null default 0,
`allow_edit` int(1) not null default 0,
`disable_automatic_recency_filters` int(1) not null default 0,
`total_fields` varchar(140),
`fields` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 15:18:03,362 WARNING database DDL Query made to DB:
create table `tabNotification Subscribed Document` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`document` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 15:18:03,470 WARNING database DDL Query made to DB:
create table `tabNote` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140),
`public` int(1) not null default 0,
`notify_on_login` int(1) not null default 0,
`notify_on_every_login` int(1) not null default 0,
`expire_notification_on` date,
`content` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `expire_notification_on`(`expire_notification_on`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 15:18:03,817 WARNING database DDL Query made to DB:
create table `tabWebhook Request Log` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`webhook` varchar(140),
`reference_document` varchar(140),
`headers` longtext,
`data` longtext,
`user` varchar(140),
`url` text,
`response` longtext,
`error` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 15:18:04,153 WARNING database DDL Query made to DB:
create table `tabGoogle Calendar` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`enable` int(1) not null default 1,
`calendar_name` varchar(140) unique,
`user` varchar(140),
`pull_from_google_calendar` int(1) not null default 1,
`sync_as_public` int(1) not null default 0,
`push_to_google_calendar` int(1) not null default 1,
`google_calendar_id` varchar(140),
`refresh_token` text,
`authorization_code` text,
`next_sync_token` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 15:18:04,428 WARNING database DDL Query made to DB:
create table `tabWebhook` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`webhook_doctype` varchar(140),
`webhook_docevent` varchar(140),
`enabled` int(1) not null default 1,
`condition` text,
`request_url` text,
`is_dynamic_url` int(1) not null default 0,
`timeout` int(11) not null default 5,
`background_jobs_queue` varchar(140),
`request_method` varchar(140) default 'POST',
`request_structure` varchar(140),
`enable_security` int(1) not null default 0,
`webhook_secret` text,
`webhook_json` longtext,
`preview_document` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 15:18:04,551 WARNING database DDL Query made to DB:
create table `tabSlack Webhook URL` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`webhook_name` varchar(140) unique,
`webhook_url` varchar(140),
`show_document_link` int(1) not null default 1,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 15:18:04,730 WARNING database DDL Query made to DB:
create table `tabIntegration Request` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`request_id` varchar(140),
`integration_request_service` varchar(140),
`is_remote_request` int(1) not null default 0,
`request_description` varchar(140),
`status` varchar(140) default 'Queued',
`url` text,
`request_headers` longtext,
`data` longtext,
`output` longtext,
`error` longtext,
`reference_doctype` varchar(140),
`reference_docname` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 15:18:04,841 WARNING database DDL Query made to DB:
create table `tabWebhook Data` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`fieldname` varchar(140),
`key` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 15:18:05,337 WARNING database DDL Query made to DB:
create table `tabOAuth Client Role` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`role` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 15:18:05,460 WARNING database DDL Query made to DB:
create table `tabGoogle Contacts` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`enable` int(1) not null default 0,
`email_id` varchar(140),
`last_sync_on` datetime(6),
`authorization_code` text,
`refresh_token` text,
`next_sync_token` text,
`pull_from_google_contacts` int(1) not null default 0,
`push_to_google_contacts` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 15:18:05,630 WARNING database DDL Query made to DB:
create table `tabLDAP Group Mapping` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`ldap_group` varchar(140),
`erpnext_role` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 15:18:05,786 WARNING database DDL Query made to DB:
create table `tabQuery Parameters` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`key` varchar(140),
`value` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 15:18:05,938 WARNING database DDL Query made to DB:
create table `tabSocial Login Key` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`enable_social_login` int(1) not null default 0,
`social_login_provider` varchar(140) default 'Custom',
`client_id` varchar(140),
`provider_name` varchar(140),
`client_secret` text,
`icon` varchar(140),
`base_url` varchar(140),
`sign_ups` varchar(140),
`authorize_url` varchar(140),
`access_token_url` varchar(140),
`redirect_url` varchar(140),
`api_endpoint` varchar(140),
`custom_base_url` int(1) not null default 0,
`api_endpoint_args` longtext,
`auth_url_data` longtext,
`user_id_property` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 15:18:06,060 WARNING database DDL Query made to DB:
create table `tabConnected App` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`provider_name` varchar(140),
`openid_configuration` varchar(140),
`client_id` varchar(140),
`redirect_uri` varchar(140),
`client_secret` text,
`authorization_uri` text,
`token_uri` varchar(140),
`revocation_uri` varchar(140),
`userinfo_uri` varchar(140),
`introspection_uri` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 15:18:06,368 WARNING database DDL Query made to DB:
create table `tabWebhook Header` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`key` text,
`value` text,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 15:18:06,456 WARNING database DDL Query made to DB:
create table `tabOAuth Scope` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`scope` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 15:18:06,580 WARNING database DDL Query made to DB:
create table `tabOAuth Bearer Token` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`client` varchar(140),
`user` varchar(140),
`scopes` text,
`access_token` varchar(140) unique,
`refresh_token` varchar(140),
`expiration_time` datetime(6),
`expires_in` int(11) not null default 0,
`status` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 15:18:06,959 WARNING database DDL Query made to DB:
create table `tabOAuth Client` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`client_id` varchar(140),
`app_name` varchar(140),
`user` varchar(140),
`client_secret` varchar(140),
`skip_authorization` int(1) not null default 0,
`scopes` text default 'all openid',
`redirect_uris` text,
`default_redirect_uri` varchar(140),
`grant_type` varchar(140),
`response_type` varchar(140) default 'Code',
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 15:18:07,110 WARNING database DDL Query made to DB:
create table `tabToken Cache` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`user` varchar(140),
`connected_app` varchar(140),
`provider_name` varchar(140),
`access_token` text,
`refresh_token` text,
`expires_in` int(11) not null default 0,
`state` varchar(140),
`success_uri` varchar(140),
`token_type` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 15:18:07,249 WARNING database DDL Query made to DB:
create table `tabOAuth Authorization Code` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`client` varchar(140),
`user` varchar(140),
`scopes` text,
`authorization_code` varchar(140) unique,
`expiration_time` datetime(6),
`redirect_uri_bound_to_authorization_code` varchar(140),
`validity` varchar(140),
`nonce` varchar(140),
`code_challenge` varchar(140),
`code_challenge_method` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 15:18:07,432 WARNING database DDL Query made to DB:
create table `tabPrint Heading` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`print_heading` varchar(140),
`description` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 15:18:07,704 WARNING database DDL Query made to DB:
create table `tabLetter Head` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`letter_head_name` varchar(140) unique,
`source` varchar(140),
`footer_source` varchar(140) default 'HTML',
`disabled` int(1) not null default 0,
`is_default` int(1) not null default 0,
`image` text,
`image_height` decimal(21,9) not null default 0,
`image_width` decimal(21,9) not null default 0,
`align` varchar(140) default 'Left',
`content` longtext,
`footer` longtext,
`footer_image` text,
`footer_image_height` decimal(21,9) not null default 0,
`footer_image_width` decimal(21,9) not null default 0,
`footer_align` varchar(140),
`header_script` longtext,
`footer_script` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `is_default`(`is_default`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 15:18:07,826 WARNING database DDL Query made to DB:
create table `tabPrint Format Field Template` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`document_type` varchar(140),
`field` varchar(140),
`template_file` varchar(140),
`module` varchar(140),
`standard` int(1) not null default 0,
`template` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 15:18:08,010 WARNING database DDL Query made to DB:
create table `tabNetwork Printer Settings` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`server_ip` varchar(140) default 'localhost',
`port` int(11) not null default 631,
`printer_name` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 15:18:08,405 WARNING database DDL Query made to DB:
create table `tabContact Phone` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`phone` varchar(140),
`is_primary_phone` int(1) not null default 0,
`is_primary_mobile_no` int(1) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 15:18:08,658 WARNING database DDL Query made to DB:
create table `tabContact Email` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`email_id` varchar(140),
`is_primary` int(1) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 15:18:08,772 WARNING database DDL Query made to DB:
create table `tabGender` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`gender` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 15:18:08,872 WARNING database DDL Query made to DB:
create table `tabSalutation` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`salutation` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 15:18:09,039 WARNING database DDL Query made to DB:
create table `tabContact` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`first_name` varchar(140),
`middle_name` varchar(140),
`last_name` varchar(140),
`full_name` varchar(140),
`email_id` varchar(140),
`user` varchar(140),
`address` varchar(140),
`sync_with_google_contacts` int(1) not null default 0,
`status` varchar(140) default 'Passive',
`salutation` varchar(140),
`designation` varchar(140),
`gender` varchar(140),
`phone` varchar(140),
`mobile_no` varchar(140),
`company_name` varchar(140),
`image` text,
`google_contacts` varchar(140),
`google_contacts_id` varchar(140),
`pulled_from_google_contacts` int(1) not null default 0,
`is_primary_contact` int(1) not null default 0,
`department` varchar(140),
`unsubscribed` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `email_id`(`email_id`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 15:18:09,229 WARNING database DDL Query made to DB:
create table `tabAddress Template` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`country` varchar(140) unique,
`is_default` int(1) not null default 0,
`template` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 15:18:09,352 WARNING database DDL Query made to DB:
create table `tabAddress` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`address_title` varchar(140),
`address_type` varchar(140),
`address_line1` varchar(240),
`address_line2` varchar(240),
`city` varchar(140),
`county` varchar(140),
`state` varchar(140),
`country` varchar(140),
`pincode` varchar(140),
`email_id` varchar(140),
`phone` varchar(140),
`fax` varchar(140),
`is_primary_address` int(1) not null default 0,
`is_shipping_address` int(1) not null default 0,
`disabled` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `city`(`city`),
index `country`(`country`),
index `pincode`(`pincode`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 15:18:09,706 WARNING database DDL Query made to DB:
create table `tabReview Level` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`level_name` varchar(140) unique,
`role` varchar(140) unique,
`review_points` int(11) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 15:18:09,847 WARNING database DDL Query made to DB:
create table `tabEnergy Point Log` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`user` varchar(140),
`type` varchar(140),
`points` int(11) not null default 0,
`rule` varchar(140),
`reference_doctype` varchar(140),
`reference_name` varchar(140),
`reverted` int(1) not null default 0,
`revert_of` varchar(140),
`reason` text,
`seen` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `user`(`user`),
index `reference_name`(`reference_name`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 15:18:09,993 WARNING database DDL Query made to DB:
create table `tabEnergy Point Rule` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`enabled` int(1) not null default 1,
`rule_name` varchar(140) unique,
`reference_doctype` varchar(140),
`for_doc_event` varchar(140) default 'Custom',
`field_to_check` varchar(140),
`points` int(11) not null default 0,
`for_assigned_users` int(1) not null default 0,
`user_field` varchar(140),
`multiplier_field` varchar(140),
`max_points` int(11) not null default 0,
`apply_only_once` int(1) not null default 0,
`condition` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 15:18:10,142 WARNING database DDL Query made to DB:
create table `tabAuto Repeat` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`reference_doctype` varchar(140),
`reference_document` varchar(140),
`submit_on_creation` int(1) not null default 0,
`start_date` date,
`end_date` date,
`disabled` int(1) not null default 0,
`frequency` varchar(140),
`repeat_on_day` int(11) not null default 0,
`repeat_on_last_day` int(1) not null default 0,
`next_schedule_date` date,
`notify_by_email` int(1) not null default 0,
`recipients` text,
`template` varchar(140),
`subject` varchar(140),
`message` text default 'Please find attached {{ doc.doctype }} #{{ doc.name }}',
`print_format` varchar(140),
`status` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `next_schedule_date`(`next_schedule_date`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 15:18:10,389 WARNING database DDL Query made to DB:
create table `tabReminder` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`user` varchar(140),
`remind_at` datetime(6),
`description` text,
`reminder_doctype` varchar(140),
`reminder_docname` varchar(140),
`notified` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `user`(`user`),
index `remind_at`(`remind_at`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 15:18:10,531 WARNING database DDL Query made to DB:
create table `tabAssignment Rule` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`document_type` varchar(140),
`due_date_based_on` varchar(140),
`priority` int(11) not null default 0,
`disabled` int(1) not null default 0,
`description` text default 'Automatic Assignment',
`assign_condition` longtext,
`unassign_condition` longtext,
`close_condition` longtext,
`rule` varchar(140),
`field` varchar(140),
`last_user` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 15:18:10,643 WARNING database DDL Query made to DB:
create table `tabMilestone` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`reference_type` varchar(140),
`reference_name` varchar(140),
`track_field` varchar(140),
`value` varchar(140),
`milestone_tracker` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `reference_type`(`reference_type`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 15:18:10,770 WARNING database DDL Query made to DB:
ALTER TABLE `tabMilestone`
				ADD INDEX IF NOT EXISTS `reference_type_reference_name_index`(reference_type, reference_name)
2025-07-15 15:18:10,873 WARNING database DDL Query made to DB:
create table `tabAssignment Rule User` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`user` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 15:18:10,979 WARNING database DDL Query made to DB:
create table `tabMilestone Tracker` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`document_type` varchar(140) unique,
`track_field` varchar(140),
`disabled` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 15:18:11,092 WARNING database DDL Query made to DB:
create table `tabAssignment Rule Day` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`day` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 15:18:11,227 WARNING database DDL Query made to DB:
create table `tabAuto Repeat Day` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`day` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 15:19:19,738 WARNING database DDL Query made to DB:
create table `tabHD Article` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140),
`status` varchar(140) default 'Draft',
`published_on` datetime(6),
`views` int(11) not null default 0,
`category` varchar(140),
`author` varchar(140),
`content` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 15:19:19,839 WARNING database DDL Query made to DB:
create table `tabHD Synonyms` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`word` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 15:19:19,908 WARNING database DDL Query made to DB:
create table `tabHD Customer` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`image` text,
`customer_name` varchar(140) unique,
`domain` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 15:19:19,982 WARNING database DDL Query made to DB:
create table `tabHD Holiday` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`holiday_date` date,
`weekly_off` int(1) not null default 0,
`description` longtext,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 15:19:20,046 WARNING database DDL Query made to DB:
create table `tabHD Ticket Template` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`template_name` varchar(140) unique,
`about` longtext,
`description_template` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 15:19:20,113 WARNING database DDL Query made to DB:
create table `tabHD Article Category` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`category_name` varchar(140),
`description` text,
`icon` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 15:19:20,187 WARNING database DDL Query made to DB:
create table `tabHD Article Feedback` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`article` varchar(140),
`user` varchar(140),
`feedback` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 15:19:20,260 WARNING database DDL Query made to DB:
create table `tabHD Service Holiday List` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`holiday_list_name` varchar(140) unique,
`from_date` date,
`to_date` date,
`total_holidays` int(11) not null default 0,
`weekly_off` varchar(140),
`color` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 15:19:20,333 WARNING database DDL Query made to DB:
create table `tabHD Agent` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`user` varchar(140),
`agent_name` varchar(140),
`user_image` varchar(140),
`is_active` int(1) not null default 1,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 15:19:20,539 WARNING database DDL Query made to DB:
create sequence if not exists hd_ticket_id_seq nocache nocycle
2025-07-15 15:19:20,552 WARNING database DDL Query made to DB:
create table `tabHD Ticket` (
			name bigint primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`subject` varchar(140),
`raised_by` varchar(140),
`status` varchar(140) default 'Open',
`priority` varchar(140),
`ticket_type` varchar(140),
`agent_group` varchar(140),
`summary` longtext,
`description` longtext,
`template` varchar(140),
`sla` varchar(140),
`response_by` datetime(6),
`agreement_status` varchar(140),
`resolution_by` datetime(6),
`service_level_agreement_creation` datetime(6),
`on_hold_since` datetime(6),
`total_hold_time` decimal(21,9),
`first_response_time` decimal(21,9),
`first_responded_on` datetime(6),
`avg_response_time` decimal(21,9),
`resolution_details` longtext,
`opening_date` date,
`opening_time` time(6),
`resolution_date` datetime(6),
`resolution_time` decimal(21,9),
`user_resolution_time` decimal(21,9),
`contact` varchar(140),
`customer` varchar(140),
`email_account` varchar(140),
`via_customer_portal` int(1) not null default 0,
`attachment` text,
`content_type` varchar(140),
`is_merged` int(1) not null default 0,
`merged_with` varchar(140),
`ticket_split_from` varchar(140),
`feedback_rating` decimal(3,2),
`feedback_text` varchar(140),
`feedback` varchar(140),
`feedback_extra` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
`_seen` text,
index `status`(`status`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 15:19:20,613 WARNING database DDL Query made to DB:
create sequence if not exists hd_preset_filter_item_id_seq nocache nocycle
2025-07-15 15:19:20,624 WARNING database DDL Query made to DB:
create table `tabHD Preset Filter Item` (
			name bigint primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`label` varchar(140),
`fieldname` varchar(140),
`filter_type` varchar(140),
`value` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 15:19:20,705 WARNING database DDL Query made to DB:
create table `tabHD View` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`label` varchar(140),
`icon` varchar(140),
`user` varchar(140),
`is_default` int(1) not null default 0,
`is_customer_portal` int(1) not null default 0,
`type` varchar(140) default 'list',
`dt` varchar(140),
`route_name` varchar(140),
`pinned` int(1) not null default 0,
`public` int(1) not null default 0,
`filters` longtext,
`order_by` longtext,
`load_default_columns` int(1) not null default 0,
`columns` longtext,
`rows` longtext,
`group_by_field` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 15:19:20,768 WARNING database DDL Query made to DB:
create table `tabHD Service Level Priority` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`default_priority` int(1) not null default 0,
`priority` varchar(140),
`response_time` decimal(21,9),
`resolution_time` decimal(21,9),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 15:19:20,832 WARNING database DDL Query made to DB:
create table `tabHD Preset Filter` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140),
`reference_doctype` varchar(140),
`type` varchar(140),
`user` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 15:19:20,891 WARNING database DDL Query made to DB:
create table `tabHD Canned Response` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140),
`message` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 15:19:20,951 WARNING database DDL Query made to DB:
create sequence if not exists hd_team_member_id_seq nocache nocycle
2025-07-15 15:19:20,961 WARNING database DDL Query made to DB:
create table `tabHD Team Member` (
			name bigint primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`user` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 15:19:21,029 WARNING database DDL Query made to DB:
create table `tabHD Notification` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`read` int(1) not null default 0,
`user_from` varchar(140),
`user_to` varchar(140),
`notification_type` varchar(140),
`reference_ticket` varchar(140),
`reference_comment` varchar(140),
`message` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 15:19:21,096 WARNING database DDL Query made to DB:
create table `tabHD Ticket Priority` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`integer_value` int(11) not null default 0,
`description` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 15:19:21,171 WARNING database DDL Query made to DB:
create table `tabHD Organization` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`organization_name` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 15:19:21,227 WARNING database DDL Query made to DB:
create table `tabHD Synonym` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`synonym` varchar(140) unique,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 15:19:21,289 WARNING database DDL Query made to DB:
create table `tabHD Team` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`team_name` varchar(140) unique,
`assignment_rule` varchar(140),
`ignore_restrictions` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 15:19:21,365 WARNING database DDL Query made to DB:
create table `tabHD Ticket Activity` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`ticket` varchar(140),
`action` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `ticket`(`ticket`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 15:19:21,445 WARNING database DDL Query made to DB:
create table `tabHD Escalation Rule` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`is_enabled` int(1) not null default 0,
`priority` varchar(140),
`team` varchar(140),
`ticket_type` varchar(140),
`to_agent` varchar(140),
`to_team` varchar(140),
`to_priority` varchar(140),
`to_ticket_type` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 15:19:21,509 WARNING database DDL Query made to DB:
create table `tabHD Service Day` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`workday` varchar(140),
`start_time` time(6),
`end_time` time(6),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 15:19:21,568 WARNING database DDL Query made to DB:
create table `tabHD Ticket Feedback Option` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`rating` decimal(3,2),
`label` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 15:19:21,634 WARNING database DDL Query made to DB:
create table `tabHD Desk Account Request` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`request_key` varchar(140),
`ip_address` varchar(140),
`email` varchar(140) unique,
`user` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 15:19:21,695 WARNING database DDL Query made to DB:
create table `tabHD Ticket Template Field` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`fieldname` varchar(140),
`url_method` varchar(140),
`required` int(1) not null default 0,
`hide_from_customer` int(1) not null default 0,
`placeholder` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 15:19:21,769 WARNING database DDL Query made to DB:
create table `tabHD Portal Signup Request` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`request_key` varchar(140),
`user` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 15:19:21,851 WARNING database DDL Query made to DB:
create table `tabHD Ticket Comment` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`reference_ticket` varchar(140),
`commented_by` varchar(140),
`is_pinned` int(1) not null default 0,
`content` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `reference_ticket`(`reference_ticket`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 15:19:21,913 WARNING database DDL Query made to DB:
create table `tabHD Pause Service Level Agreement On Status` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`status` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 15:19:21,971 WARNING database DDL Query made to DB:
create table `tabHD Organization Contact Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`contact` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 15:19:22,038 WARNING database DDL Query made to DB:
create table `tabHD Ticket Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`is_system` int(1) not null default 0,
`description` text,
`priority` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 15:19:22,110 WARNING database DDL Query made to DB:
create table `tabHD Form Script` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`dt` varchar(140) default 'HD Ticket',
`apply_to` varchar(140) default 'Form',
`enabled` int(1) not null default 0,
`apply_to_customer_portal` int(1) not null default 0,
`apply_on_new_page` int(1) not null default 0,
`script` longtext default 'function setupForm({doc, updateField, call, router, $dialog, createToast ,applyFilters}) {\n    return {\n        actions: [],\n        onChange:{\n//          works only for new ticket page\n            \"fieldname\":(newVal)=>console.log(newVal)\n        }\n    }\n}',
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 15:19:22,209 WARNING database DDL Query made to DB:
create table `tabHD Service Level Agreement` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`default_priority` varchar(140),
`service_level` varchar(140) unique,
`enabled` int(1) not null default 1,
`default_sla` int(1) not null default 0,
`condition` longtext,
`start_date` date,
`end_date` date,
`apply_sla_for_resolution` int(1) not null default 1,
`holiday_list` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 15:19:22,279 WARNING database DDL Query made to DB:
create table `tabHD Action` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`is_enabled` int(1) not null default 0,
`button_label` varchar(140),
`button_icon` varchar(140),
`is_external_link` int(1) not null default 0,
`action` longtext,
`cond_hidden` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 15:19:22,344 WARNING database DDL Query made to DB:
create table `tabHD Stopword` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`word` varchar(140) unique,
`enabled` int(1) not null default 1,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 15:19:22,417 WARNING database DDL Query made to DB:
create table `tabHD Support Search Source` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`source_name` varchar(140),
`source_type` varchar(140),
`base_url` varchar(140),
`query_route` varchar(140),
`search_term_param_name` varchar(140),
`response_result_key_path` varchar(140),
`post_route` varchar(140),
`post_route_key_list` varchar(140),
`post_title_key` varchar(140),
`post_description_key` varchar(140),
`source_doctype` varchar(140),
`result_title_field` varchar(140),
`result_preview_field` varchar(140),
`result_route_field` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 15:19:22,469 WARNING database DDL Query made to DB:
create table `tabHD Service Level Agreement Fulfilled On Status` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`status` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 15:19:36,181 WARNING database DDL Query made to DB:
create table `tabWiki Feedback` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`wiki_page` varchar(140),
`status` varchar(140) default 'Open',
`rating` decimal(3,2),
`feedback` text,
`email_id` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 15:19:36,263 WARNING database DDL Query made to DB:
create table `tabWiki Page Revision Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`wiki_page` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 15:19:36,321 WARNING database DDL Query made to DB:
create table `tabWiki Sidebar` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`wiki_page` varchar(140) unique,
`parent_label` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 15:19:36,394 WARNING database DDL Query made to DB:
create table `tabWiki Space` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`space_name` varchar(140),
`route` varchar(140) unique,
`app_switcher_logo` text,
`light_mode_logo` text,
`dark_mode_logo` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 15:19:36,462 WARNING database DDL Query made to DB:
create table `tabWiki Page Revision` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`content` longtext,
`raised_by` varchar(140),
`raised_by_username` varchar(140),
`message` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 15:19:36,603 WARNING database DDL Query made to DB:
create table `tabWiki Page Patch` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`wiki_page` varchar(140),
`new_title` varchar(140),
`new_sidebar_group` varchar(140),
`message` text,
`raised_by` varchar(140),
`status` varchar(140) default 'Under Review',
`approved_by` varchar(140),
`new` int(1) not null default 0,
`orignal_code` longtext,
`new_code` longtext,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
`_seen` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 15:19:36,663 WARNING database DDL Query made to DB:
create table `tabWiki App Switcher List Table` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`app_title` varchar(140),
`wiki_space` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 15:19:36,733 WARNING database DDL Query made to DB:
create table `tabWiki Page` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140),
`route` varchar(140) unique,
`published` int(1) not null default 0,
`allow_guest` int(1) not null default 1,
`content` longtext default 'No Content',
`meta_description` text,
`meta_image` text,
`meta_keywords` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 15:19:36,791 WARNING database DDL Query made to DB:
create table `tabWiki Group Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`parent_label` varchar(140),
`wiki_page` varchar(140),
`hide_on_sidebar` int(1) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-15 15:20:10,371 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-07-15 15:27:39,258 WARNING database DDL Query made to DB:
DROP USER IF EXISTS '_880bafd8de04e164'@'localhost'
2025-07-15 15:27:41,317 WARNING database DDL Query made to DB:
DROP DATABASE IF EXISTS `_880bafd8de04e164`
2025-07-15 15:27:41,323 WARNING database DDL Query made to DB:
CREATE USER '_880bafd8de04e164'@'localhost' IDENTIFIED BY 'jEU8KfafEzdZFcgn'
2025-07-15 15:27:41,329 WARNING database DDL Query made to DB:
CREATE DATABASE `_880bafd8de04e164` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci
2025-07-15 15:28:19,777 WARNING database DDL Query made to DB:
create table if not exists `__Auth` (
				`doctype` VARCHAR(140) NOT NULL,
				`name` VARCHAR(255) NOT NULL,
				`fieldname` VARCHAR(140) NOT NULL,
				`password` TEXT NOT NULL,
				`encrypted` INT(1) NOT NULL DEFAULT 0,
				PRIMARY KEY (`doctype`, `name`, `fieldname`)
			) ENGINE=InnoDB ROW_FORMAT=DYNAMIC CHARACTER SET=utf8mb4 COLLATE=utf8mb4_unicode_ci
2025-07-15 15:28:19,787 WARNING database DDL Query made to DB:
create table if not exists __UserSettings (
			`user` VARCHAR(180) NOT NULL,
			`doctype` VARCHAR(180) NOT NULL,
			`data` TEXT,
			UNIQUE(user, doctype)
			) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
2025-07-15 15:32:22,933 WARNING database DDL Query made to DB:
DROP USER IF EXISTS '_880bafd8de04e164'@'localhost'
2025-07-15 15:32:24,783 WARNING database DDL Query made to DB:
DROP DATABASE IF EXISTS `_880bafd8de04e164`
2025-07-15 15:32:24,789 WARNING database DDL Query made to DB:
CREATE USER '_880bafd8de04e164'@'localhost' IDENTIFIED BY 'jEU8KfafEzdZFcgn'
2025-07-15 15:32:24,795 WARNING database DDL Query made to DB:
CREATE DATABASE `_880bafd8de04e164` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci
2025-07-15 15:32:44,925 WARNING database DDL Query made to DB:
create table if not exists `__Auth` (
				`doctype` VARCHAR(140) NOT NULL,
				`name` VARCHAR(255) NOT NULL,
				`fieldname` VARCHAR(140) NOT NULL,
				`password` TEXT NOT NULL,
				`encrypted` INT(1) NOT NULL DEFAULT 0,
				PRIMARY KEY (`doctype`, `name`, `fieldname`)
			) ENGINE=InnoDB ROW_FORMAT=DYNAMIC CHARACTER SET=utf8mb4 COLLATE=utf8mb4_unicode_ci
2025-07-15 15:32:44,930 WARNING database DDL Query made to DB:
create table if not exists __UserSettings (
			`user` VARCHAR(180) NOT NULL,
			`doctype` VARCHAR(180) NOT NULL,
			`data` TEXT,
			UNIQUE(user, doctype)
			) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
2025-07-15 23:51:05,366 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-07-16 09:25:41,880 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-07-16 09:47:22,803 WARNING database DDL Query made to DB:
ALTER TABLE `tabInstalled Application` ADD COLUMN `has_setup_wizard` int(1) not null default 0, ADD COLUMN `is_setup_complete` int(1) not null default 0
2025-07-16 09:47:23,855 WARNING database DDL Query made to DB:
ALTER TABLE `tabDocType` ADD COLUMN `grid_page_length` int(11) not null default 50, ADD COLUMN `protect_attached_files` int(1) not null default 0, ADD COLUMN `row_format` varchar(140) default 'Dynamic'
2025-07-16 09:47:24,104 WARNING database DDL Query made to DB:
ALTER TABLE `tabWeb Form` MODIFY `condition_json` json
2025-07-16 09:47:24,248 WARNING database DDL Query made to DB:
ALTER TABLE `tabNumber Card` ADD COLUMN `currency` varchar(140), ADD COLUMN `background_color` varchar(140)
2025-07-16 09:47:24,404 WARNING database DDL Query made to DB:
ALTER TABLE `tabDashboard Chart` ADD COLUMN `currency` varchar(140)
2025-07-16 09:47:24,539 WARNING database DDL Query made to DB:
ALTER TABLE `tabWorkspace Shortcut` ADD COLUMN `report_ref_doctype` varchar(140)
2025-07-16 09:47:24,674 WARNING database DDL Query made to DB:
ALTER TABLE `tabReport` ADD COLUMN `add_translate_data` int(1) not null default 0
2025-07-16 09:47:24,881 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Format` ADD COLUMN `pdf_generator` varchar(140) default 'wkhtmltopdf'
2025-07-16 09:47:25,163 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrepared Report` ADD COLUMN `peak_memory_usage` int(11) not null default 0
2025-07-16 09:47:25,419 WARNING database DDL Query made to DB:
ALTER TABLE `tabSMS Parameter` MODIFY `value` varchar(255)
2025-07-16 09:47:26,182 WARNING database DDL Query made to DB:
ALTER TABLE `tabScheduled Job Type` ADD COLUMN `scheduler_event` varchar(140)
2025-07-16 09:47:26,550 WARNING database DDL Query made to DB:
ALTER TABLE `tabUser` ADD INDEX `last_active_index`(`last_active`)
2025-07-16 09:47:26,772 WARNING database DDL Query made to DB:
ALTER TABLE `tabNavbar Item` ADD COLUMN `condition` longtext
2025-07-16 09:47:27,011 WARNING database DDL Query made to DB:
ALTER TABLE `tabFile`
				ADD INDEX IF NOT EXISTS `file_url_index`(file_url(100))
2025-07-16 09:47:27,384 WARNING database DDL Query made to DB:
create table `tabScheduler Event` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`scheduled_against` varchar(140),
`method` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-16 09:47:27,722 WARNING database DDL Query made to DB:
ALTER TABLE `tabWeb Page View` ADD INDEX `path_index`(`path`)
2025-07-16 09:47:28,234 WARNING database DDL Query made to DB:
ALTER TABLE `tabWorkflow Transition` ADD COLUMN `send_email_to_creator` int(1) not null default 0
2025-07-16 09:47:28,358 WARNING database DDL Query made to DB:
ALTER TABLE `tabWorkflow Document State` ADD COLUMN `send_email` int(1) not null default 1
2025-07-16 09:47:28,502 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmail Domain` ADD COLUMN `sent_folder_name` varchar(140) default 'Sent'
2025-07-16 09:47:28,881 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmail Account` ADD COLUMN `backend_app_flow` int(1) not null default 0, ADD COLUMN `sent_folder_name` varchar(140), ADD COLUMN `always_bcc` varchar(140)
2025-07-16 09:47:30,013 WARNING database DDL Query made to DB:
ALTER TABLE `tabEvent` MODIFY `google_meet_link` text
2025-07-16 09:47:30,482 WARNING database DDL Query made to DB:
ALTER TABLE `tabTag Link`
				ADD INDEX IF NOT EXISTS `document_type_document_name_index`(document_type, document_name)
2025-07-16 09:47:30,768 WARNING database DDL Query made to DB:
ALTER TABLE `tabNotification Log` MODIFY `link` text
2025-07-16 09:47:31,522 WARNING database DDL Query made to DB:
ALTER TABLE `tabList View Settings` ADD COLUMN `disable_automatic_recency_filters` int(1) not null default 0
2025-07-16 09:47:31,855 WARNING database DDL Query made to DB:
ALTER TABLE `tabWebhook Request Log` MODIFY `url` text
2025-07-16 09:47:32,147 WARNING database DDL Query made to DB:
ALTER TABLE `tabGoogle Calendar` ADD COLUMN `sync_as_public` int(1) not null default 0
2025-07-16 09:47:33,031 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-07-16 09:47:33,912 WARNING database DDL Query made to DB:
ALTER TABLE `tabBank Transaction` MODIFY `allocated_amount` decimal(21,9) not null default 0, MODIFY `withdrawal` decimal(21,9) not null default 0, MODIFY `unallocated_amount` decimal(21,9) not null default 0, MODIFY `deposit` decimal(21,9) not null default 0
2025-07-16 09:47:34,142 WARNING database DDL Query made to DB:
create table `tabPegged Currency Details` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`source_currency` varchar(140),
`pegged_against` varchar(140),
`pegged_exchange_rate` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-16 09:47:34,404 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Entry Deduction` ADD COLUMN `is_exchange_gain_loss` int(1) not null default 0
2025-07-16 09:47:34,426 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Entry Deduction` MODIFY `amount` decimal(21,9) not null default 0
2025-07-16 09:47:34,711 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Advance` ADD COLUMN `difference_posting_date` date
2025-07-16 09:47:34,732 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Advance` MODIFY `ref_exchange_rate` decimal(21,9) not null default 0, MODIFY `exchange_gain_loss` decimal(21,9) not null default 0, MODIFY `advance_amount` decimal(21,9) not null default 0, MODIFY `allocated_amount` decimal(21,9) not null default 0
2025-07-16 09:47:35,122 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Invoice Advance` ADD COLUMN `difference_posting_date` date
2025-07-16 09:47:35,143 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Invoice Advance` MODIFY `exchange_gain_loss` decimal(21,9) not null default 0, MODIFY `advance_amount` decimal(21,9) not null default 0, MODIFY `allocated_amount` decimal(21,9) not null default 0, MODIFY `ref_exchange_rate` decimal(21,9) not null default 0
2025-07-16 09:47:35,353 WARNING database DDL Query made to DB:
ALTER TABLE `tabDunning` MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `total_outstanding` decimal(21,9) not null default 0
2025-07-16 09:47:35,845 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` ADD COLUMN `distributed_discount_amount` decimal(21,9) not null default 0, ADD COLUMN `company_total_stock` decimal(21,9) not null default 0, ADD COLUMN `pos_invoice` varchar(140), ADD COLUMN `pos_invoice_item` varchar(140)
2025-07-16 09:47:35,863 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0
2025-07-16 09:47:35,900 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` ADD INDEX `pos_invoice_index`(`pos_invoice`), ADD INDEX `creation`(`creation`)
2025-07-16 09:47:36,167 WARNING database DDL Query made to DB:
ALTER TABLE `tabProcess Statement Of Accounts` ADD COLUMN `categorize_by` varchar(140) default 'Categorize by Voucher (Consolidated)'
2025-07-16 09:47:36,530 WARNING database DDL Query made to DB:
ALTER TABLE `tabAccount` MODIFY `tax_rate` decimal(21,9) not null default 0
2025-07-16 09:47:37,054 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Entry` MODIFY `base_received_amount` decimal(21,9) not null default 0, MODIFY `paid_from_account_balance` decimal(21,9) not null default 0, MODIFY `base_received_amount_after_tax` decimal(21,9) not null default 0, MODIFY `party_balance` decimal(21,9) not null default 0, MODIFY `received_amount_after_tax` decimal(21,9) not null default 0, MODIFY `total_allocated_amount` decimal(21,9) not null default 0, MODIFY `target_exchange_rate` decimal(21,9) not null default 0, MODIFY `paid_to_account_balance` decimal(21,9) not null default 0, MODIFY `base_paid_amount` decimal(21,9) not null default 0, MODIFY `difference_amount` decimal(21,9) not null default 0, MODIFY `unallocated_amount` decimal(21,9) not null default 0, MODIFY `paid_amount_after_tax` decimal(21,9) not null default 0, MODIFY `received_amount` decimal(21,9) not null default 0, MODIFY `source_exchange_rate` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `base_total_allocated_amount` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `base_paid_amount_after_tax` decimal(21,9) not null default 0
2025-07-16 09:47:37,829 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Invoice` ADD COLUMN `dispatch_address` varchar(140), ADD COLUMN `dispatch_address_display` longtext
2025-07-16 09:47:37,853 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Invoice` MODIFY `base_taxes_and_charges_deducted` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `base_taxes_and_charges_added` decimal(21,9) not null default 0, MODIFY `outstanding_amount` decimal(21,9) not null default 0, MODIFY `write_off_amount` decimal(21,9) not null default 0, MODIFY `taxes_and_charges_added` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `base_write_off_amount` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `base_paid_amount` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `base_tax_withholding_net_total` decimal(21,9) not null default 0, MODIFY `taxes_and_charges_deducted` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `total_advance` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `per_received` decimal(21,9) not null default 0
2025-07-16 09:47:38,504 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Invoice Item` ADD COLUMN `distributed_discount_amount` decimal(21,9) not null default 0
2025-07-16 09:47:38,532 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Invoice Item` MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `sales_incoming_rate` decimal(21,9) not null default 0, MODIFY `landed_cost_voucher_amount` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `rm_supp_cost` decimal(21,9) not null default 0, MODIFY `valuation_rate` decimal(21,9) not null default 0, MODIFY `item_tax_amount` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `rejected_qty` decimal(21,9) not null default 0, MODIFY `received_qty` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0
2025-07-16 09:47:38,809 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Schedule` ADD COLUMN `base_outstanding` decimal(21,9) not null default 0, ADD COLUMN `base_paid_amount` decimal(21,9) not null default 0
2025-07-16 09:47:38,829 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Schedule` MODIFY `outstanding` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `payment_amount` decimal(21,9) not null default 0, MODIFY `invoice_portion` decimal(21,9) not null default 0, MODIFY `base_payment_amount` decimal(21,9) not null default 0, MODIFY `discount` decimal(21,9) not null default 0
2025-07-16 09:47:38,857 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Schedule` ADD INDEX `creation`(`creation`)
2025-07-16 09:47:39,014 WARNING database DDL Query made to DB:
ALTER TABLE `tabTax Withheld Vouchers` MODIFY `taxable_amount` decimal(21,9) not null default 0
2025-07-16 09:47:39,163 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Entry Reference` ADD COLUMN `reconcile_effect_on` date
2025-07-16 09:47:39,180 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Entry Reference` MODIFY `allocated_amount` decimal(21,9) not null default 0, MODIFY `exchange_rate` decimal(21,9) not null default 0, MODIFY `payment_term_outstanding` decimal(21,9) not null default 0, MODIFY `outstanding_amount` decimal(21,9) not null default 0, MODIFY `exchange_gain_loss` decimal(21,9) not null default 0, MODIFY `total_amount` decimal(21,9) not null default 0
2025-07-16 09:47:39,684 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Request` ADD COLUMN `phone_number` varchar(140)
2025-07-16 09:47:39,700 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Request` MODIFY `outstanding_amount` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0
2025-07-16 09:47:40,279 WARNING database DDL Query made to DB:
ALTER TABLE `tabPOS Invoice` ADD COLUMN `company_contact_person` varchar(140)
2025-07-16 09:47:40,298 WARNING database DDL Query made to DB:
ALTER TABLE `tabPOS Invoice` MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `write_off_amount` decimal(21,9) not null default 0, MODIFY `total_advance` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `base_paid_amount` decimal(21,9) not null default 0, MODIFY `total_commission` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `outstanding_amount` decimal(21,9) not null default 0, MODIFY `amount_eligible_for_commission` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `base_write_off_amount` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `change_amount` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `loyalty_amount` decimal(21,9) not null default 0, MODIFY `base_change_amount` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `commission_rate` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0
2025-07-16 09:47:40,488 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Taxes and Charges` MODIFY `tax_amount` decimal(21,9) not null default 0, MODIFY `base_tax_amount_after_discount_amount` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `base_tax_amount` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `tax_amount_after_discount_amount` decimal(21,9) not null default 0
2025-07-16 09:47:41,363 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice` ADD COLUMN `vfd_email_sent` int(1) not null default 0, ADD COLUMN `company_contact_person` varchar(140)
2025-07-16 09:47:41,381 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice` MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `outstanding_amount` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `base_change_amount` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `total_billing_hours` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `base_paid_amount` decimal(21,9) not null default 0, MODIFY `commission_rate` decimal(21,9) not null default 0, MODIFY `total_advance` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `base_write_off_amount` decimal(21,9) not null default 0, MODIFY `loyalty_amount` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `total_commission` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `change_amount` decimal(21,9) not null default 0, MODIFY `write_off_amount` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `amount_eligible_for_commission` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0
2025-07-16 09:47:41,410 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice` ADD INDEX `project_index`(`project`)
2025-07-16 09:47:41,556 WARNING database DDL Query made to DB:
ALTER TABLE `tabAdvance Taxes and Charges` MODIFY `allocated_amount` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `tax_amount` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `base_tax_amount` decimal(21,9) not null default 0
2025-07-16 09:47:41,650 WARNING database DDL Query made to DB:
create table `tabProcess Statement Of Accounts CC` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`cc` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-16 09:47:41,943 WARNING database DDL Query made to DB:
ALTER TABLE `tabPOS Invoice Item` ADD COLUMN `distributed_discount_amount` decimal(21,9) not null default 0
2025-07-16 09:47:41,960 WARNING database DDL Query made to DB:
ALTER TABLE `tabPOS Invoice Item` MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0
2025-07-16 09:47:42,059 WARNING database DDL Query made to DB:
ALTER TABLE `tabPOS Closing Entry Taxes` MODIFY `rate` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0
2025-07-16 09:47:42,180 WARNING database DDL Query made to DB:
ALTER TABLE `tabAccounting Dimension` ADD INDEX `document_type_index`(`document_type`)
2025-07-16 09:47:42,373 WARNING database DDL Query made to DB:
ALTER TABLE `tabPOS Profile` ADD COLUMN `print_receipt_on_order_complete` int(1) not null default 0, ADD COLUMN `disable_grand_total_to_default_mop` int(1) not null default 0, ADD COLUMN `allow_partial_payment` int(1) not null default 0, ADD COLUMN `project` varchar(140)
2025-07-16 09:47:42,402 WARNING database DDL Query made to DB:
ALTER TABLE `tabPOS Profile` ADD INDEX `creation`(`creation`)
2025-07-16 09:47:42,556 WARNING database DDL Query made to DB:
ALTER TABLE `tabProcess Payment Reconciliation Log Allocations` ADD COLUMN `gain_loss_posting_date` date
