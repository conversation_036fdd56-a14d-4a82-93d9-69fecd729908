2025-07-09 09:37:31,590 WARNING database DDL Query made to DB:
create table `tabExpense Claim <PERSON>ail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`expense_date` date,
`expense_type` varchar(140),
`default_account` varchar(140),
`description` longtext,
`amount` decimal(21,9) not null default 0,
`sanctioned_amount` decimal(21,9) not null default 0,
`cost_center` varchar(140),
`project` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 09:37:31,682 WARNING database DDL Query made to DB:
create table `tabJob <PERSON>` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`designation` varchar(140),
`department` varchar(140),
`no_of_positions` int(11) not null default 0,
`expected_compensation` decimal(21,9) not null default 0,
`company` varchar(140),
`status` varchar(140),
`requested_by` varchar(140),
`requested_by_name` varchar(140),
`requested_by_dept` varchar(140),
`requested_by_designation` varchar(140),
`posting_date` date,
`completed_on` date,
`expected_by` date,
`time_to_fill` decimal(21,9),
`description` longtext,
`reason_for_requesting` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 09:37:31,759 WARNING database DDL Query made to DB:
create table `tabLeave Policy Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`leave_type` varchar(140),
`annual_allocation` decimal(21,9) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 09:37:31,893 WARNING database DDL Query made to DB:
create table `tabExit Interview` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`employee` varchar(140),
`employee_name` varchar(140),
`email` varchar(140),
`company` varchar(140),
`status` varchar(140),
`date` date,
`department` varchar(140),
`designation` varchar(140),
`reports_to` varchar(140),
`date_of_joining` date,
`relieving_date` date,
`ref_doctype` varchar(140),
`questionnaire_email_sent` int(1) not null default 0,
`reference_document_name` varchar(140),
`interview_summary` longtext,
`employee_status` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 09:37:31,981 WARNING database DDL Query made to DB:
create table `tabEmployee Grievance` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`subject` varchar(140),
`raised_by` varchar(140),
`employee_name` varchar(140),
`designation` varchar(140),
`date` date,
`status` varchar(140) default 'Open',
`reports_to` varchar(140),
`grievance_against_party` varchar(140),
`grievance_against` varchar(140),
`grievance_type` varchar(140),
`associated_document_type` varchar(140),
`associated_document` varchar(140),
`description` text,
`cause_of_grievance` text,
`resolved_by` varchar(140),
`resolution_date` date,
`employee_responsible` varchar(140),
`resolution_detail` text,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 09:37:32,077 WARNING database DDL Query made to DB:
create table `tabEmployee Performance Feedback` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`employee` varchar(140),
`employee_name` varchar(140),
`department` varchar(140),
`designation` varchar(140),
`company` varchar(140),
`reviewer` varchar(140),
`reviewer_name` varchar(140),
`reviewer_designation` varchar(140),
`user` varchar(140),
`added_on` datetime(6),
`appraisal_cycle` varchar(140),
`appraisal` varchar(140),
`total_score` decimal(21,9) not null default 0,
`feedback` longtext,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 09:37:32,258 WARNING database DDL Query made to DB:
create table `tabDaily Work Summary Group User` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`user` varchar(140),
`email` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 09:37:32,380 WARNING database DDL Query made to DB:
create table `tabExpected Skill Set` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`skill` varchar(140),
`description` text,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 09:37:32,451 WARNING database DDL Query made to DB:
create table `tabEmployee Onboarding Template` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140),
`company` varchar(140),
`department` varchar(140),
`designation` varchar(140),
`employee_grade` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 09:37:32,523 WARNING database DDL Query made to DB:
create table `tabEmployee Property History` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`property` varchar(140),
`current` varchar(140),
`new` varchar(140),
`fieldname` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 09:37:32,908 WARNING database DDL Query made to DB:
create table `tabShift Location` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`location_name` varchar(140) unique,
`checkin_radius` int(11) not null default 0,
`latitude` decimal(21,9) not null default 0,
`longitude` decimal(21,9) not null default 0,
`geolocation` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 09:37:33,066 WARNING database DDL Query made to DB:
create table `tabExpense Claim` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`employee` varchar(140),
`employee_name` varchar(140),
`department` varchar(140),
`company` varchar(140),
`expense_approver` varchar(140),
`approval_status` varchar(140) default 'Draft',
`total_sanctioned_amount` decimal(21,9) not null default 0,
`total_taxes_and_charges` decimal(21,9) not null default 0,
`total_advance_amount` decimal(21,9) not null default 0,
`grand_total` decimal(21,9) not null default 0,
`total_claimed_amount` decimal(21,9) not null default 0,
`total_amount_reimbursed` decimal(21,9) not null default 0,
`posting_date` date,
`is_paid` int(1) not null default 0,
`mode_of_payment` varchar(140),
`payable_account` varchar(140),
`clearance_date` date,
`remark` text,
`project` varchar(140),
`cost_center` varchar(140),
`status` varchar(140) default 'Draft',
`task` varchar(140),
`amended_from` varchar(140),
`delivery_trip` varchar(140),
`vehicle_log` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `employee`(`employee`),
index `approval_status`(`approval_status`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 09:37:33,260 WARNING database DDL Query made to DB:
create table `tabDepartment Approver` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`approver` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 09:37:33,386 WARNING database DDL Query made to DB:
create table `tabInterviewer` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`user` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 09:37:33,487 WARNING database DDL Query made to DB:
create table `tabOffer Term` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`offer_term` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 09:37:33,576 WARNING database DDL Query made to DB:
create table `tabFull and Final Statement` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`employee` varchar(140),
`employee_name` varchar(140),
`transaction_date` date,
`company` varchar(140),
`status` varchar(140) default 'Unpaid',
`amended_from` varchar(140),
`date_of_joining` date,
`relieving_date` date,
`designation` varchar(140),
`department` varchar(140),
`total_asset_recovery_cost` decimal(21,9) not null default 0,
`total_payable_amount` decimal(21,9) not null default 0,
`total_receivable_amount` decimal(21,9) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 09:37:33,668 WARNING database DDL Query made to DB:
create table `tabExpense Taxes and Charges` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`account_head` varchar(140),
`rate` decimal(21,9) not null default 0,
`tax_amount` decimal(21,9) not null default 0,
`total` decimal(21,9) not null default 0,
`description` text,
`cost_center` varchar(140),
`project` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 09:37:33,798 WARNING database DDL Query made to DB:
create table `tabInterview` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`interview_round` varchar(140),
`job_applicant` varchar(140),
`job_opening` varchar(140),
`designation` varchar(140),
`resume_link` varchar(140),
`status` varchar(140) default 'Pending',
`scheduled_on` date,
`from_time` time(6),
`to_time` time(6),
`expected_average_rating` decimal(3,2),
`average_rating` decimal(3,2),
`interview_summary` text,
`reminded` int(1) not null default 0,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 09:37:33,912 WARNING database DDL Query made to DB:
create table `tabTraining Event Employee` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`employee` varchar(140),
`employee_name` varchar(140),
`department` varchar(140),
`status` varchar(140) default 'Open',
`attendance` varchar(140),
`is_mandatory` int(1) not null default 1,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 09:37:33,982 WARNING database DDL Query made to DB:
create table `tabAppraisal KRA` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`kra` varchar(140),
`per_weightage` decimal(21,9) not null default 0,
`goal_completion` decimal(21,9) not null default 0,
`goal_score` decimal(21,9) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 09:37:34,073 WARNING database DDL Query made to DB:
create table `tabSkill` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`skill_name` varchar(140) unique,
`description` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 09:37:34,161 WARNING database DDL Query made to DB:
create table `tabShift Request` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`shift_type` varchar(140),
`employee` varchar(140),
`employee_name` varchar(140),
`department` varchar(140),
`status` varchar(140) default 'Draft',
`company` varchar(140),
`approver` varchar(140),
`from_date` date,
`to_date` date,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 09:37:34,278 WARNING database DDL Query made to DB:
create table `tabExpense Claim Account` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`company` varchar(140),
`default_account` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 09:37:34,413 WARNING database DDL Query made to DB:
create table `tabAppointment Letter Template` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`template_name` varchar(140) unique,
`introduction` longtext,
`closing_notes` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 09:37:34,487 WARNING database DDL Query made to DB:
create table `tabVehicle Service` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`service_item` varchar(140),
`type` varchar(140),
`frequency` varchar(140),
`expense_amount` decimal(21,9) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 09:37:34,571 WARNING database DDL Query made to DB:
create table `tabFull and Final Outstanding Statement` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`component` varchar(140),
`reference_document_type` varchar(140),
`reference_document` varchar(140),
`account` varchar(140),
`paid_via_salary_slip` int(1) not null default 0,
`amount` decimal(21,9) not null default 0,
`status` varchar(140) default 'Unsettled',
`remark` text,
index `reference_document`(`reference_document`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 09:37:34,646 WARNING database DDL Query made to DB:
create table `tabEmployee Grade` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`default_salary_structure` varchar(140),
`currency` varchar(140),
`default_base_pay` decimal(21,9) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 09:37:34,797 WARNING database DDL Query made to DB:
create table `tabJob Opening` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`job_title` varchar(140),
`designation` varchar(140),
`status` varchar(140),
`posted_on` datetime(6),
`closes_on` date,
`closed_on` date,
`company` varchar(140),
`department` varchar(140),
`employment_type` varchar(140),
`location` varchar(140),
`staffing_plan` varchar(140),
`planned_vacancies` int(11) not null default 0,
`job_requisition` varchar(140),
`vacancies` int(11) not null default 0,
`publish` int(1) not null default 0,
`route` varchar(140) unique,
`publish_applications_received` int(1) not null default 1,
`job_application_route` varchar(140),
`description` longtext,
`currency` varchar(140),
`lower_range` decimal(21,9) not null default 0,
`upper_range` decimal(21,9) not null default 0,
`salary_per` varchar(140) default 'Month',
`publish_salary_range` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 09:37:34,891 WARNING database DDL Query made to DB:
create table `tabEmployee Skill Map` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`employee` varchar(140) unique,
`employee_name` varchar(140),
`designation` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 09:37:35,000 WARNING database DDL Query made to DB:
create table `tabEmployee Advance` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`employee` varchar(140),
`employee_name` varchar(140),
`posting_date` date,
`company` varchar(140),
`department` varchar(140),
`currency` varchar(140),
`exchange_rate` decimal(21,9) not null default 0,
`purpose` text,
`advance_amount` decimal(21,9) not null default 0,
`paid_amount` decimal(21,9) not null default 0,
`pending_amount` decimal(21,9) not null default 0,
`claimed_amount` decimal(21,9) not null default 0,
`return_amount` decimal(21,9) not null default 0,
`advance_account` varchar(140),
`mode_of_payment` varchar(140),
`repay_unclaimed_amount_from_salary` int(1) not null default 0,
`status` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 09:37:35,144 WARNING database DDL Query made to DB:
create table `tabCompensatory Leave Request` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`employee` varchar(140),
`employee_name` varchar(140),
`department` varchar(140),
`leave_type` varchar(140),
`leave_allocation` varchar(140),
`work_from_date` date,
`work_end_date` date,
`half_day` int(1) not null default 0,
`half_day_date` date,
`reason` text,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 09:37:35,301 WARNING database DDL Query made to DB:
create table `tabShift Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`start_time` time(6),
`end_time` time(6),
`holiday_list` varchar(140),
`color` varchar(140) default 'Blue',
`enable_auto_attendance` int(1) not null default 0,
`determine_check_in_and_check_out` varchar(140),
`working_hours_calculation_based_on` varchar(140),
`begin_check_in_before_shift_start_time` int(11) not null default 60,
`allow_check_out_after_shift_end_time` int(11) not null default 60,
`mark_auto_attendance_on_holidays` int(1) not null default 0,
`working_hours_threshold_for_half_day` decimal(21,9) not null default 0,
`working_hours_threshold_for_absent` decimal(21,9) not null default 0,
`process_attendance_after` date,
`last_sync_of_checkin` datetime(6),
`auto_update_last_sync` int(1) not null default 0,
`enable_late_entry_marking` int(1) not null default 0,
`late_entry_grace_period` int(11) not null default 0,
`enable_early_exit_marking` int(1) not null default 0,
`early_exit_grace_period` int(11) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 09:37:35,425 WARNING database DDL Query made to DB:
create table `tabEmployee Feedback Criteria` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`criteria` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 09:37:35,508 WARNING database DDL Query made to DB:
create table `tabVehicle Service Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`service_item` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 09:37:35,735 WARNING database DDL Query made to DB:
create table `tabEmployee Boarding Activity` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`activity_name` varchar(140),
`user` varchar(140),
`role` varchar(140),
`begin_on` int(11) not null default 0,
`duration` int(11) not null default 0,
`task` varchar(140),
`task_weight` decimal(21,9) not null default 0,
`required_for_employee_creation` int(1) not null default 0,
`description` longtext,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 09:37:35,807 WARNING database DDL Query made to DB:
create table `tabEmployee Separation Template` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140),
`company` varchar(140),
`department` varchar(140),
`designation` varchar(140),
`employee_grade` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 09:37:35,983 WARNING database DDL Query made to DB:
create sequence if not exists pwa_notification_id_seq nocache nocycle
2025-07-09 09:37:36,008 WARNING database DDL Query made to DB:
create table `tabPWA Notification` (
			name bigint primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`to_user` varchar(140),
`from_user` varchar(140),
`message` longtext,
`read` int(1) not null default 0,
`reference_document_type` varchar(140),
`reference_document_name` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `to_user`(`to_user`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 09:37:36,151 WARNING database DDL Query made to DB:
create table `tabGoal` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`goal_name` varchar(140),
`is_group` int(1) not null default 0,
`parent_goal` varchar(140),
`progress` decimal(21,9) not null default 0,
`status` varchar(140) default 'Pending',
`employee` varchar(140),
`employee_name` varchar(140),
`company` varchar(140),
`user` varchar(140),
`start_date` date,
`end_date` date,
`appraisal_cycle` varchar(140),
`kra` varchar(140),
`description` longtext,
`lft` int(11) not null default 0,
`rgt` int(11) not null default 0,
`old_parent` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 09:37:36,297 WARNING database DDL Query made to DB:
create table `tabAppraisal Template` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`template_title` varchar(140) unique,
`description` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 09:37:36,527 WARNING database DDL Query made to DB:
create table `tabTraining Program` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`training_program` varchar(140),
`status` varchar(140) default 'Scheduled',
`company` varchar(140),
`trainer_name` varchar(140),
`trainer_email` varchar(140),
`supplier` varchar(140),
`contact_number` varchar(140),
`description` longtext,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 09:37:36,611 WARNING database DDL Query made to DB:
create table `tabFull and Final Asset` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`reference` varchar(140),
`asset_name` varchar(140),
`date` datetime(6),
`actual_cost` decimal(21,9) not null default 0,
`cost` decimal(21,9) not null default 0,
`account` varchar(140),
`action` varchar(140) default 'Return',
`status` varchar(140),
`description` text,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 09:37:36,722 WARNING database DDL Query made to DB:
create table `tabInterview Feedback` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`interview` varchar(140),
`interview_round` varchar(140),
`job_applicant` varchar(140),
`interviewer` varchar(140),
`result` varchar(140),
`average_rating` decimal(3,2),
`feedback` text,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 09:37:36,862 WARNING database DDL Query made to DB:
create table `tabEmployee Referral` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`first_name` varchar(140),
`last_name` varchar(140),
`full_name` varchar(140),
`date` date,
`status` varchar(140),
`for_designation` varchar(140),
`email` varchar(140) unique,
`contact_no` varchar(140),
`resume_link` varchar(140),
`current_employer` varchar(140),
`current_job_title` varchar(140),
`resume` text,
`referrer` varchar(140),
`referrer_name` varchar(140),
`is_applicable_for_referral_bonus` int(1) not null default 1,
`referral_payment_status` varchar(140),
`department` varchar(140),
`qualification_reason` longtext,
`work_references` longtext,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 09:37:36,942 WARNING database DDL Query made to DB:
create table `tabTraining Result` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`training_event` varchar(140) unique,
`amended_from` varchar(140),
`employee_emails` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 09:37:37,011 WARNING database DDL Query made to DB:
create table `tabPurpose of Travel` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`purpose_of_travel` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 09:37:37,085 WARNING database DDL Query made to DB:
create table `tabDaily Work Summary Group` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`enabled` int(1) not null default 1,
`send_emails_at` varchar(140),
`holiday_list` varchar(140),
`subject` varchar(140) default 'What did you work on today?',
`message` longtext default '<p>Please share what did you do today. If you reply by midnight, your response will be recorded!</p>',
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 09:37:37,156 WARNING database DDL Query made to DB:
create table `tabJob Applicant Source` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`source_name` varchar(140),
`details` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 09:37:37,294 WARNING database DDL Query made to DB:
create table `tabInterview Round` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`round_name` varchar(140) unique,
`interview_type` varchar(140),
`expected_average_rating` decimal(3,2),
`designation` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 09:37:37,443 WARNING database DDL Query made to DB:
create table `tabJob Applicant` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`applicant_name` varchar(140),
`email_id` varchar(140),
`phone_number` varchar(140),
`country` varchar(140),
`job_title` varchar(140),
`designation` varchar(140),
`status` varchar(140),
`source` varchar(140),
`source_name` varchar(140),
`employee_referral` varchar(140),
`applicant_rating` decimal(3,2),
`notes` varchar(140),
`cover_letter` text,
`resume_attachment` text,
`resume_link` varchar(140),
`currency` varchar(140),
`lower_range` decimal(21,9) not null default 0,
`upper_range` decimal(21,9) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `job_title`(`job_title`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 09:37:37,512 WARNING database DDL Query made to DB:
create table `tabJob Offer Term` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`offer_term` varchar(140),
`value` text,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 09:37:37,646 WARNING database DDL Query made to DB:
create table `tabLeave Application` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`employee` varchar(140),
`employee_name` varchar(140),
`leave_type` varchar(140),
`company` varchar(140),
`department` varchar(140),
`from_date` date,
`to_date` date,
`half_day` int(1) not null default 0,
`half_day_date` date,
`total_leave_days` decimal(21,9) not null default 0,
`description` text,
`leave_balance` decimal(21,9) not null default 0,
`leave_approver` varchar(140),
`leave_approver_name` varchar(140),
`follow_via_email` int(1) not null default 1,
`posting_date` date,
`status` varchar(140) default 'Open',
`salary_slip` varchar(140),
`color` varchar(140),
`letter_head` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `employee`(`employee`),
index `leave_type`(`leave_type`),
index `from_date`(`from_date`),
index `to_date`(`to_date`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 09:37:37,764 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Application`
				ADD INDEX IF NOT EXISTS `employee_from_date_to_date_index`(employee, from_date, to_date)
2025-07-09 09:37:37,874 WARNING database DDL Query made to DB:
create table `tabLeave Ledger Entry` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`employee` varchar(140),
`employee_name` varchar(140),
`leave_type` varchar(140),
`transaction_type` varchar(140),
`transaction_name` varchar(140),
`company` varchar(140),
`leaves` decimal(21,9) not null default 0,
`from_date` date,
`to_date` date,
`holiday_list` varchar(140),
`is_carry_forward` int(1) not null default 0,
`is_expired` int(1) not null default 0,
`is_lwp` int(1) not null default 0,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `employee`(`employee`),
index `transaction_type`(`transaction_type`),
index `transaction_name`(`transaction_name`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 09:37:37,974 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Ledger Entry`
				ADD INDEX IF NOT EXISTS `transaction_type_transaction_name_index`(transaction_type, transaction_name)
2025-07-09 09:37:38,077 WARNING database DDL Query made to DB:
create table `tabImport CSV` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`csv_file` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 09:37:38,232 WARNING database DDL Query made to DB:
create table `tabAppraisee` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`employee` varchar(140),
`employee_name` varchar(140),
`appraisal_template` varchar(140),
`department` varchar(140),
`designation` varchar(140),
`branch` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 09:37:38,355 WARNING database DDL Query made to DB:
create table `tabShift Schedule` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`shift_type` varchar(140),
`frequency` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `amended_from`(`amended_from`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 09:37:38,485 WARNING database DDL Query made to DB:
create table `tabDaily Work Summary` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`daily_work_summary_group` varchar(140),
`status` varchar(140) default 'Open',
`email_sent_to` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 09:37:38,584 WARNING database DDL Query made to DB:
create table `tabAppraisal Cycle` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`cycle_name` varchar(140) unique,
`company` varchar(140),
`status` varchar(140) default 'Not Started',
`start_date` date,
`end_date` date,
`description` longtext,
`kra_evaluation_method` varchar(140) default 'Automated Based on Goal Progress',
`calculate_final_score_based_on_formula` int(1) not null default 0,
`final_score_formula` longtext,
`branch` varchar(140),
`department` varchar(140),
`designation` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 09:37:38,678 WARNING database DDL Query made to DB:
create table `tabAttendance Request` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`employee` varchar(140),
`employee_name` varchar(140),
`department` varchar(140),
`company` varchar(140),
`from_date` date,
`to_date` date,
`half_day` int(1) not null default 0,
`half_day_date` date,
`include_holidays` int(1) not null default 0,
`shift` varchar(140),
`reason` varchar(140),
`explanation` text,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 09:37:38,820 WARNING database DDL Query made to DB:
create table `tabVehicle Log` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`license_plate` varchar(140),
`employee` varchar(140),
`model` varchar(140),
`make` varchar(140),
`date` date,
`odometer` int(11) not null default 0,
`last_odometer` int(11) not null default 0,
`fuel_qty` decimal(21,9) not null default 0,
`price` decimal(21,9) not null default 0,
`supplier` varchar(140),
`invoice` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 09:37:38,952 WARNING database DDL Query made to DB:
create table `tabTraining Event` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`event_name` varchar(140) unique,
`training_program` varchar(140),
`event_status` varchar(140),
`has_certificate` int(1) not null default 0,
`type` varchar(140),
`level` varchar(140),
`company` varchar(140),
`trainer_name` varchar(140),
`trainer_email` varchar(140),
`supplier` varchar(140),
`contact_number` varchar(140),
`course` varchar(140),
`location` varchar(140),
`start_time` datetime(6),
`end_time` datetime(6),
`introduction` longtext,
`amended_from` varchar(140),
`employee_emails` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 09:37:39,062 WARNING database DDL Query made to DB:
create table `tabStaffing Plan Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`designation` varchar(140),
`vacancies` int(11) not null default 0,
`estimated_cost_per_position` decimal(21,9) not null default 0,
`total_estimated_cost` decimal(21,9) not null default 0,
`current_count` int(11) not null default 0,
`current_openings` int(11) not null default 0,
`number_of_positions` int(11) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 09:37:39,167 WARNING database DDL Query made to DB:
create table `tabLeave Allocation` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`employee` varchar(140),
`employee_name` varchar(140),
`department` varchar(140),
`company` varchar(140),
`leave_type` varchar(140),
`from_date` date,
`to_date` date,
`new_leaves_allocated` decimal(21,9) not null default 0,
`carry_forward` int(1) not null default 0,
`unused_leaves` decimal(21,9) not null default 0,
`total_leaves_allocated` decimal(21,9) not null default 0,
`total_leaves_encashed` decimal(21,9) not null default 0,
`compensatory_request` varchar(140),
`leave_period` varchar(140),
`leave_policy` varchar(140),
`leave_policy_assignment` varchar(140),
`carry_forwarded_leaves_count` decimal(21,9) not null default 0,
`expired` int(1) not null default 0,
`amended_from` varchar(140),
`description` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `employee`(`employee`),
index `employee_name`(`employee_name`),
index `leave_type`(`leave_type`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 09:37:39,287 WARNING database DDL Query made to DB:
create table `tabEmployee Feedback Rating` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`criteria` varchar(140),
`per_weightage` decimal(21,9) not null default 0,
`rating` decimal(3,2),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 09:37:39,422 WARNING database DDL Query made to DB:
create table `tabShift Assignment` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`employee` varchar(140),
`employee_name` varchar(140),
`company` varchar(140),
`department` varchar(140),
`shift_type` varchar(140),
`shift_location` varchar(140),
`status` varchar(140) default 'Active',
`start_date` date,
`end_date` date,
`shift_request` varchar(140),
`shift_schedule_assignment` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `employee`(`employee`),
index `shift_type`(`shift_type`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 09:37:39,531 WARNING database DDL Query made to DB:
create table `tabLeave Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`leave_type_name` varchar(140) unique,
`max_leaves_allowed` decimal(21,9) not null default 0,
`applicable_after` int(11) not null default 0,
`max_continuous_days_allowed` int(11) not null default 0,
`is_carry_forward` int(1) not null default 0,
`is_lwp` int(1) not null default 0,
`is_ppl` int(1) not null default 0,
`fraction_of_daily_salary_per_leave` decimal(21,9) not null default 0,
`is_optional_leave` int(1) not null default 0,
`allow_negative` int(1) not null default 0,
`allow_over_allocation` int(1) not null default 0,
`include_holiday` int(1) not null default 0,
`is_compensatory` int(1) not null default 0,
`maximum_carry_forwarded_leaves` decimal(21,9) not null default 0,
`expire_carry_forwarded_leaves_after_days` int(11) not null default 0,
`allow_encashment` int(1) not null default 0,
`max_encashable_leaves` int(11) not null default 0,
`non_encashable_leaves` int(11) not null default 0,
`earning_component` varchar(140),
`is_earned_leave` int(1) not null default 0,
`earned_leave_frequency` varchar(140),
`allocate_on_day` varchar(140) default 'Last Day',
`rounding` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 09:37:39,708 WARNING database DDL Query made to DB:
create table `tabAttendance` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`employee` varchar(140),
`employee_name` varchar(140),
`working_hours` decimal(21,9) not null default 0,
`status` varchar(140) default 'Present',
`leave_type` varchar(140),
`leave_application` varchar(140),
`attendance_date` date,
`company` varchar(140),
`department` varchar(140),
`attendance_request` varchar(140),
`half_day_status` varchar(140),
`shift` varchar(140),
`in_time` datetime(6),
`out_time` datetime(6),
`late_entry` int(1) not null default 0,
`early_exit` int(1) not null default 0,
`amended_from` varchar(140),
`modify_half_day_status` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `employee`(`employee`),
index `status`(`status`),
index `attendance_date`(`attendance_date`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 09:37:39,872 WARNING database DDL Query made to DB:
create table `tabEmployee Checkin` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`employee` varchar(140),
`employee_name` varchar(140),
`log_type` varchar(140),
`shift` varchar(140),
`time` datetime(6),
`device_id` varchar(140),
`skip_auto_attendance` int(1) not null default 0,
`attendance` varchar(140),
`latitude` decimal(21,9) not null default 0,
`longitude` decimal(21,9) not null default 0,
`geolocation` longtext,
`shift_start` datetime(6),
`shift_end` datetime(6),
`offshift` int(1) not null default 0,
`shift_actual_start` datetime(6),
`shift_actual_end` datetime(6),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `employee`(`employee`),
index `shift`(`shift`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 09:37:40,001 WARNING database DDL Query made to DB:
create table `tabTraining Result Employee` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`employee` varchar(140),
`employee_name` varchar(140),
`department` varchar(140),
`hours` decimal(21,9) not null default 0,
`grade` varchar(140),
`comments` text,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 09:37:40,070 WARNING database DDL Query made to DB:
create table `tabLeave Block List Allow` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`allow_user` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 09:37:40,250 WARNING database DDL Query made to DB:
create table `tabEmployee Training` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`training` varchar(140),
`training_date` date,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 09:37:40,432 WARNING database DDL Query made to DB:
create table `tabInterview Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`interviewer` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 09:37:40,520 WARNING database DDL Query made to DB:
create table `tabEmployee Separation` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`employee` varchar(140),
`employee_name` varchar(140),
`department` varchar(140),
`designation` varchar(140),
`employee_grade` varchar(140),
`company` varchar(140),
`boarding_status` varchar(140) default 'Pending',
`resignation_letter_date` date,
`boarding_begins_on` date,
`project` varchar(140),
`employee_separation_template` varchar(140),
`notify_users_by_email` int(1) not null default 0,
`exit_interview` longtext,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 09:37:40,618 WARNING database DDL Query made to DB:
create table `tabTravel Itinerary` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`travel_from` varchar(140),
`travel_to` varchar(140),
`mode_of_travel` varchar(140),
`meal_preference` varchar(140),
`travel_advance_required` int(1) not null default 0,
`advance_amount` varchar(140),
`departure_date` datetime(6),
`arrival_date` datetime(6),
`lodging_required` int(1) not null default 0,
`preferred_area_for_lodging` varchar(140),
`check_in_date` date,
`check_out_date` date,
`other_details` text,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 09:37:40,807 WARNING database DDL Query made to DB:
create table `tabEmployee Onboarding` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`job_applicant` varchar(140),
`job_offer` varchar(140),
`employee_onboarding_template` varchar(140),
`company` varchar(140),
`boarding_status` varchar(140) default 'Pending',
`project` varchar(140),
`employee` varchar(140),
`employee_name` varchar(140),
`department` varchar(140),
`designation` varchar(140),
`employee_grade` varchar(140),
`holiday_list` varchar(140),
`date_of_joining` date,
`boarding_begins_on` date,
`notify_users_by_email` int(1) not null default 0,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 09:37:40,896 WARNING database DDL Query made to DB:
create table `tabEmployment Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`employee_type_name` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 09:37:41,016 WARNING database DDL Query made to DB:
create table `tabEmployee Health Insurance` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`health_insurance_name` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 09:37:41,119 WARNING database DDL Query made to DB:
create table `tabEmployee Transfer` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`employee` varchar(140),
`employee_name` varchar(140),
`transfer_date` date,
`company` varchar(140),
`new_company` varchar(140),
`department` varchar(140),
`reallocate_leaves` int(1) not null default 0,
`create_new_employee_id` int(1) not null default 0,
`new_employee_id` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 09:37:41,411 WARNING database DDL Query made to DB:
create table `tabAppraisal Template Goal` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`key_result_area` varchar(140),
`per_weightage` decimal(21,9) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 09:37:41,494 WARNING database DDL Query made to DB:
create table `tabLeave Block List` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`leave_block_list_name` varchar(140) unique,
`company` varchar(140),
`applies_to_all_departments` int(1) not null default 0,
`leave_type` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 09:37:41,578 WARNING database DDL Query made to DB:
create table `tabLeave Policy Assignment` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`employee` varchar(140),
`employee_name` varchar(140),
`company` varchar(140),
`leave_policy` varchar(140),
`carry_forward` int(1) not null default 0,
`assignment_based_on` varchar(140),
`leave_period` varchar(140),
`effective_from` date,
`effective_to` date,
`leaves_allocated` int(1) not null default 0,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 09:37:41,699 WARNING database DDL Query made to DB:
create table `tabAppointment Letter content` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140),
`description` longtext,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 09:37:41,771 WARNING database DDL Query made to DB:
create table `tabAppraisal Goal` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`kra` text,
`per_weightage` decimal(21,9) not null default 0,
`score` decimal(21,9) not null default 0,
`score_earned` decimal(21,9) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 09:37:41,844 WARNING database DDL Query made to DB:
create table `tabJob Offer Term Template` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 09:37:41,924 WARNING database DDL Query made to DB:
create table `tabGrievance Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`description` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 09:37:42,860 WARNING database DDL Query made to DB:
create table `tabEmployee Tax Exemption Proof Submission Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`exemption_sub_category` varchar(140),
`exemption_category` varchar(140),
`max_amount` decimal(21,9) not null default 0,
`amount` decimal(21,9) not null default 0,
`type_of_proof` varchar(140),
`attach_proof` text,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 09:37:42,931 WARNING database DDL Query made to DB:
create table `tabTaxable Salary Slab` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`from_amount` decimal(21,9) not null default 0,
`to_amount` decimal(21,9) not null default 0,
`percent_deduction` decimal(21,9) not null default 0,
`condition` longtext,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 09:37:42,996 WARNING database DDL Query made to DB:
create table `tabEmployee Tax Exemption Category` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`max_amount` decimal(21,9) not null default 0,
`is_active` int(1) not null default 1,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 09:37:43,090 WARNING database DDL Query made to DB:
create table `tabRetention Bonus` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`employee` varchar(140),
`employee_name` varchar(140),
`department` varchar(140),
`company` varchar(140),
`date_of_joining` varchar(140),
`salary_component` varchar(140),
`bonus_amount` decimal(21,9) not null default 0,
`bonus_payment_date` date,
`currency` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 09:37:43,363 WARNING database DDL Query made to DB:
create table `tabSalary Slip` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`employee` varchar(140),
`employee_name` varchar(140),
`company` varchar(140),
`department` varchar(140),
`designation` varchar(140),
`branch` varchar(140),
`posting_date` date,
`letter_head` varchar(140),
`status` varchar(140),
`salary_withholding` varchar(140),
`salary_withholding_cycle` varchar(140),
`currency` varchar(140),
`exchange_rate` decimal(21,9) not null default 1.0,
`payroll_frequency` varchar(140),
`start_date` date,
`end_date` date,
`salary_structure` varchar(140),
`payroll_entry` varchar(140),
`mode_of_payment` varchar(140),
`salary_slip_based_on_timesheet` int(1) not null default 0,
`deduct_tax_for_unclaimed_employee_benefits` int(1) not null default 0,
`deduct_tax_for_unsubmitted_tax_exemption_proof` int(1) not null default 0,
`total_working_days` decimal(21,9) not null default 0,
`unmarked_days` decimal(21,9) not null default 0,
`leave_without_pay` decimal(21,9) not null default 0,
`absent_days` decimal(21,9) not null default 0,
`payment_days` decimal(21,9) not null default 0,
`total_working_hours` decimal(21,9) not null default 0,
`hour_rate` decimal(21,9) not null default 0,
`base_hour_rate` decimal(21,9) not null default 0,
`gross_pay` decimal(21,9) not null default 0,
`base_gross_pay` decimal(21,9) not null default 0,
`gross_year_to_date` decimal(21,9) not null default 0,
`base_gross_year_to_date` decimal(21,9) not null default 0,
`total_deduction` decimal(21,9) not null default 0,
`base_total_deduction` decimal(21,9) not null default 0,
`net_pay` decimal(21,9) not null default 0,
`base_net_pay` decimal(21,9) not null default 0,
`rounded_total` decimal(21,9) not null default 0,
`base_rounded_total` decimal(21,9) not null default 0,
`year_to_date` decimal(21,9) not null default 0,
`base_year_to_date` decimal(21,9) not null default 0,
`month_to_date` decimal(21,9) not null default 0,
`base_month_to_date` decimal(21,9) not null default 0,
`total_in_words` varchar(240),
`base_total_in_words` varchar(240),
`ctc` decimal(21,9) not null default 0,
`income_from_other_sources` decimal(21,9) not null default 0,
`total_earnings` decimal(21,9) not null default 0,
`non_taxable_earnings` decimal(21,9) not null default 0,
`standard_tax_exemption_amount` decimal(21,9) not null default 0,
`tax_exemption_declaration` decimal(21,9) not null default 0,
`deductions_before_tax_calculation` decimal(21,9) not null default 0,
`annual_taxable_amount` decimal(21,9) not null default 0,
`income_tax_deducted_till_date` decimal(21,9) not null default 0,
`current_month_income_tax` decimal(21,9) not null default 0,
`future_income_tax_deductions` decimal(21,9) not null default 0,
`total_income_tax` decimal(21,9) not null default 0,
`journal_entry` varchar(140),
`amended_from` varchar(140),
`bank_name` varchar(140),
`bank_account_no` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `employee`(`employee`),
index `start_date`(`start_date`),
index `end_date`(`end_date`),
index `salary_structure`(`salary_structure`),
index `payroll_entry`(`payroll_entry`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 09:37:43,440 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip`
				ADD INDEX IF NOT EXISTS `employee_start_date_end_date_index`(employee, start_date, end_date)
2025-07-09 09:37:43,509 WARNING database DDL Query made to DB:
create table `tabEmployee Benefit Application` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`employee` varchar(140),
`employee_name` varchar(140),
`department` varchar(140),
`date` date,
`payroll_period` varchar(140),
`company` varchar(140),
`amended_from` varchar(140),
`currency` varchar(140),
`max_benefits` decimal(21,9) not null default 0,
`remaining_benefit` decimal(21,9) not null default 0,
`total_amount` decimal(21,9) not null default 0,
`pro_rata_dispensed_amount` decimal(21,9) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 09:37:43,599 WARNING database DDL Query made to DB:
create table `tabPayroll Period` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`company` varchar(140),
`start_date` date,
`end_date` date,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 09:37:43,683 WARNING database DDL Query made to DB:
create table `tabPayroll Employee Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`employee` varchar(140),
`employee_name` varchar(140),
`department` varchar(140),
`designation` varchar(140),
`is_salary_withheld` int(1) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 09:37:43,762 WARNING database DDL Query made to DB:
create table `tabIncome Tax Slab` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`disabled` int(1) not null default 0,
`effective_from` date,
`company` varchar(140),
`currency` varchar(140),
`standard_tax_exemption_amount` decimal(21,9) not null default 0,
`allow_tax_exemption` int(1) not null default 0,
`amended_from` varchar(140),
`tax_relief_limit` decimal(21,9) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 09:37:43,842 WARNING database DDL Query made to DB:
create table `tabEmployee Cost Center` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`cost_center` varchar(140),
`percentage` int(11) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 09:37:43,973 WARNING database DDL Query made to DB:
create table `tabPayroll Period Date` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`start_date` date,
`end_date` date,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 09:37:44,050 WARNING database DDL Query made to DB:
create table `tabEmployee Tax Exemption Declaration` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`employee` varchar(140),
`employee_name` varchar(140),
`department` varchar(140),
`company` varchar(140),
`payroll_period` varchar(140),
`currency` varchar(140),
`amended_from` varchar(140),
`total_declared_amount` decimal(21,9) not null default 0,
`total_exemption_amount` decimal(21,9) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 09:37:44,141 WARNING database DDL Query made to DB:
create table `tabGratuity Applicable Component` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`salary_component` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 09:37:44,286 WARNING database DDL Query made to DB:
create table `tabSalary Structure Assignment` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`employee` varchar(140),
`employee_name` varchar(140),
`department` varchar(140),
`designation` varchar(140),
`grade` varchar(140),
`salary_structure` varchar(140),
`from_date` date,
`income_tax_slab` varchar(140),
`company` varchar(140),
`payroll_payable_account` varchar(140),
`currency` varchar(140),
`base` decimal(21,9) not null default 0,
`variable` decimal(21,9) not null default 0,
`amended_from` varchar(140),
`taxable_earnings_till_date` decimal(21,9) not null default 0,
`tax_deducted_till_date` decimal(21,9) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `employee`(`employee`),
index `salary_structure`(`salary_structure`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 09:37:44,396 WARNING database DDL Query made to DB:
create table `tabSalary Component Account` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`company` varchar(140),
`account` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 09:37:44,501 WARNING database DDL Query made to DB:
create table `tabSalary Slip Leave` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`leave_type` varchar(140),
`total_allocated_leaves` decimal(21,9) not null default 0,
`expired_leaves` decimal(21,9) not null default 0,
`used_leaves` decimal(21,9) not null default 0,
`pending_leaves` decimal(21,9) not null default 0,
`available_leaves` decimal(21,9) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 09:37:44,602 WARNING database DDL Query made to DB:
create table `tabGratuity Rule Slab` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`from_year` int(11) not null default 0,
`to_year` int(11) not null default 0,
`fraction_of_applicable_earnings` decimal(21,9) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 09:37:44,683 WARNING database DDL Query made to DB:
create table `tabEmployee Tax Exemption Declaration Category` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`exemption_sub_category` varchar(140),
`exemption_category` varchar(140),
`max_amount` decimal(21,9) not null default 0,
`amount` decimal(21,9) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 09:37:44,788 WARNING database DDL Query made to DB:
create table `tabAdditional Salary` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`employee` varchar(140),
`employee_name` varchar(140),
`department` varchar(140),
`company` varchar(140),
`is_recurring` int(1) not null default 0,
`disabled` int(1) not null default 0,
`from_date` date,
`to_date` date,
`payroll_date` date,
`amended_from` varchar(140),
`salary_component` varchar(140),
`type` varchar(140),
`currency` varchar(140),
`amount` decimal(21,9) not null default 0,
`deduct_full_tax_on_selected_payroll_date` int(1) not null default 0,
`overwrite_salary_structure_amount` int(1) not null default 1,
`ref_doctype` varchar(140),
`ref_docname` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `employee`(`employee`),
index `payroll_date`(`payroll_date`),
index `salary_component`(`salary_component`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 09:37:44,868 WARNING database DDL Query made to DB:
create table `tabEmployee Tax Exemption Sub Category` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`exemption_category` varchar(140),
`max_amount` decimal(21,9) not null default 0,
`is_active` int(1) not null default 1,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 09:37:44,959 WARNING database DDL Query made to DB:
create table `tabEmployee Benefit Application Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`earning_component` varchar(140),
`pay_against_benefit_claim` int(1) not null default 0,
`max_benefit_amount` decimal(21,9) not null default 0,
`amount` decimal(21,9) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 09:37:45,038 WARNING database DDL Query made to DB:
create table `tabEmployee Incentive` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`employee` varchar(140),
`employee_name` varchar(140),
`amended_from` varchar(140),
`company` varchar(140),
`department` varchar(140),
`salary_component` varchar(140),
`currency` varchar(140),
`payroll_date` date,
`incentive_amount` decimal(21,9) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 09:37:45,116 WARNING database DDL Query made to DB:
create table `tabSalary Withholding Cycle` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`from_date` date,
`to_date` date,
`is_salary_released` int(1) not null default 0,
`journal_entry` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 09:37:45,264 WARNING database DDL Query made to DB:
create table `tabSalary Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`salary_component` varchar(140),
`abbr` varchar(140),
`amount` decimal(21,9) not null default 0,
`year_to_date` decimal(21,9) not null default 0,
`additional_salary` varchar(140),
`is_recurring_additional_salary` int(1) not null default 0,
`statistical_component` int(1) not null default 0,
`depends_on_payment_days` int(1) not null default 0,
`exempted_from_income_tax` int(1) not null default 0,
`is_tax_applicable` int(1) not null default 0,
`is_flexible_benefit` int(1) not null default 0,
`variable_based_on_taxable_salary` int(1) not null default 0,
`do_not_include_in_total` int(1) not null default 0,
`deduct_full_tax_on_selected_payroll_date` int(1) not null default 0,
`condition` longtext,
`amount_based_on_formula` int(1) not null default 0,
`formula` longtext,
`default_amount` decimal(21,9) not null default 0,
`additional_amount` decimal(21,9) not null default 0,
`tax_on_flexible_benefit` decimal(21,9) not null default 0,
`tax_on_additional_salary` decimal(21,9) not null default 0,
index `salary_component`(`salary_component`),
index `exempted_from_income_tax`(`exempted_from_income_tax`),
index `is_tax_applicable`(`is_tax_applicable`),
index `variable_based_on_taxable_salary`(`variable_based_on_taxable_salary`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 09:37:45,429 WARNING database DDL Query made to DB:
create table `tabIncome Tax Slab Other Charges` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`description` varchar(140),
`percent` decimal(21,9) not null default 0,
`min_taxable_income` decimal(21,9) not null default 0,
`max_taxable_income` decimal(21,9) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 09:37:45,526 WARNING database DDL Query made to DB:
create table `tabEmployee Benefit Claim` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`employee` varchar(140),
`employee_name` varchar(140),
`department` varchar(140),
`claim_date` date,
`currency` varchar(140),
`company` varchar(140),
`amended_from` varchar(140),
`earning_component` varchar(140),
`max_amount_eligible` decimal(21,9) not null default 0,
`pay_against_benefit_claim` int(1) not null default 0,
`claimed_amount` decimal(21,9) not null default 0,
`salary_slip` varchar(140),
`attachments` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 09:37:45,612 WARNING database DDL Query made to DB:
create table `tabSalary Slip Timesheet` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`time_sheet` varchar(140),
`working_hours` decimal(21,9) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 09:37:45,697 WARNING database DDL Query made to DB:
create table `tabEmployee Tax Exemption Proof Submission` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`employee` varchar(140),
`employee_name` varchar(140),
`department` varchar(140),
`currency` varchar(140),
`amended_from` varchar(140),
`submission_date` date,
`payroll_period` varchar(140),
`company` varchar(140),
`total_actual_amount` decimal(21,9) not null default 0,
`exemption_amount` decimal(21,9) not null default 0,
`attachments` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 09:37:45,791 WARNING database DDL Query made to DB:
create table `tabGratuity Rule` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`disable` int(1) not null default 0,
`calculate_gratuity_amount_based_on` varchar(140),
`total_working_days_per_year` int(11) not null default 365,
`work_experience_calculation_function` varchar(140) default 'Round off Work Experience',
`minimum_year_for_gratuity` int(11) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 09:37:45,884 WARNING database DDL Query made to DB:
create table `tabEmployee Other Income` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`employee` varchar(140),
`employee_name` varchar(140),
`amended_from` varchar(140),
`company` varchar(140),
`payroll_period` varchar(140),
`source` varchar(140),
`amount` decimal(21,9) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `employee`(`employee`),
index `company`(`company`),
index `payroll_period`(`payroll_period`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 09:37:46,002 WARNING database DDL Query made to DB:
create table `tabPayroll Entry` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`posting_date` date,
`company` varchar(140),
`currency` varchar(140),
`exchange_rate` decimal(21,9) not null default 0,
`payroll_payable_account` varchar(140),
`status` varchar(140),
`salary_slip_based_on_timesheet` int(1) not null default 0,
`payroll_frequency` varchar(140),
`start_date` date,
`end_date` date,
`deduct_tax_for_unclaimed_employee_benefits` int(1) not null default 0,
`deduct_tax_for_unsubmitted_tax_exemption_proof` int(1) not null default 0,
`branch` varchar(140),
`department` varchar(140),
`designation` varchar(140),
`grade` varchar(140),
`number_of_employees` int(11) not null default 0,
`validate_attendance` int(1) not null default 0,
`cost_center` varchar(140),
`project` varchar(140),
`payment_account` varchar(140),
`bank_account` varchar(140),
`salary_slips_created` int(1) not null default 0,
`salary_slips_submitted` int(1) not null default 0,
`error_message` text,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 09:37:46,078 WARNING database DDL Query made to DB:
create table `tabSalary Slip Loan` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`loan` varchar(140),
`loan_product` varchar(140),
`loan_account` varchar(140),
`interest_income_account` varchar(140),
`principal_amount` decimal(21,9) not null default 0,
`interest_amount` decimal(21,9) not null default 0,
`total_payment` decimal(21,9) not null default 0,
`loan_repayment_entry` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 09:37:46,173 WARNING database DDL Query made to DB:
create table `tabGratuity` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`employee` varchar(140),
`employee_name` varchar(140),
`department` varchar(140),
`designation` varchar(140),
`current_work_experience` decimal(21,9) not null default 0,
`posting_date` date,
`gratuity_rule` varchar(140),
`status` varchar(140) default 'Draft',
`company` varchar(140),
`amended_from` varchar(140),
`pay_via_salary_slip` int(1) not null default 1,
`amount` decimal(21,9) not null default 0,
`paid_amount` decimal(21,9) not null default 0,
`payroll_date` date,
`salary_component` varchar(140),
`cost_center` varchar(140),
`mode_of_payment` varchar(140),
`expense_account` varchar(140),
`payable_account` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `employee`(`employee`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 09:37:46,303 WARNING database DDL Query made to DB:
create table `tabSalary Withholding` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`employee` varchar(140),
`employee_name` varchar(140),
`company` varchar(140),
`posting_date` date,
`payroll_frequency` varchar(140),
`number_of_withholding_cycles` int(11) not null default 0,
`status` varchar(140) default 'Draft',
`from_date` date,
`to_date` date,
`date_of_joining` date,
`relieving_date` date,
`reason_for_withholding_salary` text,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `employee`(`employee`),
index `amended_from`(`amended_from`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 09:37:46,438 WARNING database DDL Query made to DB:
create table `tabSalary Structure` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`company` varchar(140),
`letter_head` varchar(140),
`is_active` varchar(140) default 'Yes',
`is_default` varchar(140) default 'No',
`currency` varchar(140),
`amended_from` varchar(140),
`leave_encashment_amount_per_day` decimal(21,9) not null default 0,
`max_benefits` decimal(21,9) not null default 0,
`salary_slip_based_on_timesheet` int(1) not null default 0,
`payroll_frequency` varchar(140) default 'Monthly',
`salary_component` varchar(140),
`hour_rate` decimal(21,9) not null default 0,
`total_earning` decimal(21,9) not null default 0,
`total_deduction` decimal(21,9) not null default 0,
`net_pay` decimal(21,9) not null default 0,
`mode_of_payment` varchar(140),
`payment_account` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `company`(`company`),
index `currency`(`currency`),
index `salary_slip_based_on_timesheet`(`salary_slip_based_on_timesheet`),
index `payroll_frequency`(`payroll_frequency`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
