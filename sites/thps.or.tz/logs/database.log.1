2024-11-04 10:05:34,039 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabClearing Charges`
2024-11-04 10:05:34,081 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabCF Delivery Note`
2024-11-04 10:05:34,118 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabRisk Assessment`
2024-11-04 10:05:34,150 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabTRA Document`
2024-11-04 10:05:34,190 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabShipping Line`
2024-11-04 10:05:34,224 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabClearing Document Type`
2024-11-04 10:05:34,273 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabShipment Clearance`
2024-11-04 10:05:34,311 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabDocument Attachments`
2024-11-04 10:05:34,344 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabContainer Interchange`
2024-11-04 10:05:34,375 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabAdditional Expense`
2024-11-04 10:05:34,413 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabClearing Document`
2024-11-04 10:05:34,447 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabShip clearance Document`
2024-11-04 10:05:34,476 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabClearing Document Type Attribute`
2024-11-04 10:05:34,509 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabMode of Transport`
2024-11-04 10:05:34,561 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabClearing File`
2024-11-04 10:05:34,598 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabClearing File Document`
2024-11-04 10:05:34,627 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabPort clearance Document`
2024-11-04 10:05:34,666 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabClearing Charge Detail`
2024-11-04 10:05:34,720 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabRisk`
2024-11-04 10:05:34,761 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabAirplane`
2024-11-04 10:05:34,788 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabMode of Transport Detail`
2024-11-04 10:05:34,839 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabPhysical Verification`
2024-11-04 10:05:34,879 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabTRA Clearance`
2024-11-04 10:05:34,909 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabClearing Document Attribute`
2024-11-04 10:05:34,939 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabPhysical Verification Image`
2024-11-10 20:26:59,457 WARNING database DDL Query made to DB:
create table `tabLoan Security Pledge` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`applicant_type` varchar(140),
`applicant` varchar(140),
`loan` varchar(140),
`loan_application` varchar(140),
`company` varchar(140),
`pledge_time` datetime(6),
`status` varchar(140) default 'Requested',
`total_security_value` decimal(21,9) not null default 0,
`maximum_loan_value` decimal(21,9) not null default 0,
`reference_no` varchar(140),
`description` text,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2024-11-10 20:26:59,888 WARNING database DDL Query made to DB:
create table `tabLoan Interest Accrual` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`loan` varchar(140),
`loan_product` varchar(140),
`applicant_type` varchar(140),
`applicant` varchar(140),
`interest_income_account` varchar(140),
`loan_account` varchar(140),
`company` varchar(140),
`posting_date` date,
`due_date` date,
`accrual_type` varchar(140),
`is_term_loan` int(1) not null default 0,
`is_npa` int(1) not null default 0,
`pending_principal_amount` decimal(21,9) not null default 0,
`payable_principal_amount` decimal(21,9) not null default 0,
`paid_principal_amount` decimal(21,9) not null default 0,
`interest_amount` decimal(21,9) not null default 0,
`total_pending_interest_amount` decimal(21,9) not null default 0,
`paid_interest_amount` decimal(21,9) not null default 0,
`penalty_amount` decimal(21,9) not null default 0,
`process_loan_interest_accrual` varchar(140),
`repayment_schedule_name` varchar(140),
`last_accrual_date` date,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2024-11-10 20:27:00,097 WARNING database DDL Query made to DB:
create table `tabLoan Repayment Schedule` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`loan` varchar(140),
`company` varchar(140),
`loan_restructure` varchar(140),
`loan_amount` decimal(21,9) not null default 0,
`rate_of_interest` decimal(21,9) not null default 0,
`posting_date` date,
`adjusted_interest` decimal(21,9) not null default 0,
`loan_product` varchar(140),
`repayment_schedule_type` varchar(140),
`repayment_method` varchar(140),
`repayment_periods` int(11) not null default 0,
`monthly_repayment_amount` decimal(21,9) not null default 0,
`repayment_start_date` date,
`status` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2024-11-10 20:27:00,174 WARNING database DDL Query made to DB:
create table `tabLoan Partner Shareable` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`shareable_type` varchar(140),
`sharing_parameter` varchar(140),
`partner_collection_percentage` decimal(21,9) not null default 0,
`company_collection_percentage` decimal(21,9) not null default 0,
`partner_loan_amount_percentage` decimal(21,9) not null default 0,
`minimum_partner_loan_amount_percentage` decimal(21,9) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2024-11-10 20:27:00,269 WARNING database DDL Query made to DB:
create table `tabLoan Disbursement` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`against_loan` varchar(140),
`posting_date` date,
`applicant_type` varchar(140),
`loan_product` varchar(140),
`monthly_repayment_amount` decimal(21,9) not null default 0,
`company` varchar(140),
`applicant` varchar(140),
`is_term_loan` int(1) not null default 0,
`withhold_security_deposit` int(1) not null default 0,
`disbursement_date` date,
`clearance_date` date,
`disbursed_amount` decimal(21,9) not null default 0,
`cost_center` varchar(140),
`disbursement_account` varchar(140),
`loan_account` varchar(140),
`bank_account` varchar(140),
`reference_date` date,
`reference_number` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2024-11-10 20:27:00,409 WARNING database DDL Query made to DB:
create table `tabLoan Charge Reference` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`sales_invoice` varchar(140),
`pending_charge_amount` decimal(21,9) not null default 0,
`allocated_amount` decimal(21,9) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2024-11-10 20:27:00,537 WARNING database DDL Query made to DB:
create table `tabLoan` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`applicant_type` varchar(140),
`applicant` varchar(140),
`applicant_name` varchar(140),
`loan_application` varchar(140),
`branch` varchar(140),
`company` varchar(140),
`posting_date` date,
`status` varchar(140) default 'Sanctioned',
`loan_product` varchar(140),
`repayment_schedule_type` varchar(140),
`loan_amount` decimal(21,9) not null default 0,
`rate_of_interest` decimal(21,9) not null default 0,
`is_secured_loan` int(1) not null default 0,
`disbursement_date` date,
`closure_date` date,
`disbursed_amount` decimal(21,9) not null default 0,
`maximum_loan_amount` decimal(21,9) not null default 0,
`repayment_method` varchar(140),
`repayment_periods` int(11) not null default 0,
`monthly_repayment_amount` decimal(21,9) not null default 0,
`repayment_start_date` date,
`is_term_loan` int(1) not null default 0,
`days_past_due` int(11) not null default 0,
`classification_code` varchar(140),
`classification_name` varchar(140),
`loan_restructure_count` int(11) not null default 0,
`watch_period_end_date` date,
`tenure_post_restructure` int(11) not null default 0,
`cost_center` varchar(140),
`mode_of_payment` varchar(140),
`disbursement_account` varchar(140),
`payment_account` varchar(140),
`loan_account` varchar(140),
`interest_income_account` varchar(140),
`penalty_income_account` varchar(140),
`total_payment` decimal(21,9) not null default 0,
`total_principal_paid` decimal(21,9) not null default 0,
`written_off_amount` decimal(21,9) not null default 0,
`refund_amount` decimal(21,9) not null default 0,
`debit_adjustment_amount` decimal(21,9) not null default 0,
`credit_adjustment_amount` decimal(21,9) not null default 0,
`total_interest_payable` decimal(21,9) not null default 0,
`total_amount_paid` decimal(21,9) not null default 0,
`is_npa` int(1) not null default 0,
`manual_npa` int(1) not null default 0,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2024-11-10 20:27:00,639 WARNING database DDL Query made to DB:
create table `tabLoan Product Loan Partner` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`loan_partner` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2024-11-10 20:27:00,721 WARNING database DDL Query made to DB:
create table `tabProcess Loan Restructure Limit` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`posting_date` date,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2024-11-10 20:27:00,811 WARNING database DDL Query made to DB:
create table `tabProcess Loan Interest Accrual` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`posting_date` date,
`loan_product` varchar(140),
`loan` varchar(140),
`process_type` varchar(140),
`accrual_type` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2024-11-10 20:27:00,953 WARNING database DDL Query made to DB:
create table `tabLoan Restructure Limit Log` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`branch` varchar(140),
`date` date,
`company` varchar(140),
`principal_outstanding` decimal(21,9) not null default 0,
`limit_percent` decimal(21,9) not null default 0,
`limit_amount` decimal(21,9) not null default 0,
`utilized_limit` decimal(21,9) not null default 0,
`in_process_limit` decimal(21,9) not null default 0,
`available_limit` decimal(21,9) not null default 0,
`delinquent_principal_outstanding` decimal(21,9) not null default 0,
`delinquent_limit_percent` decimal(21,9) not null default 0,
`delinquent_limit_amount` decimal(21,9) not null default 0,
`delinquent_utilized_limit` decimal(21,9) not null default 0,
`delinquent_in_process_limit` decimal(21,9) not null default 0,
`delinquent_available_limit` decimal(21,9) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2024-11-10 20:27:01,043 WARNING database DDL Query made to DB:
create table `tabLoan Classification Range` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`classification_code` varchar(140),
`classification_name` varchar(140),
`min_dpd_range` int(11) not null default 0,
`max_dpd_range` int(11) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2024-11-10 20:27:01,130 WARNING database DDL Query made to DB:
create table `tabLoan Classification` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`classification_code` varchar(140) unique,
`classification_name` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2024-11-10 20:27:01,233 WARNING database DDL Query made to DB:
create table `tabLoan Write Off` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`loan` varchar(140),
`applicant_type` varchar(140),
`applicant` varchar(140),
`company` varchar(140),
`posting_date` date,
`cost_center` varchar(140),
`write_off_account` varchar(140),
`write_off_amount` decimal(21,9) not null default 0,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2024-11-10 20:27:01,547 WARNING database DDL Query made to DB:
create table `tabLoan Refund` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`loan` varchar(140),
`applicant_type` varchar(140),
`applicant` varchar(140),
`company` varchar(140),
`posting_date` date,
`cost_center` varchar(140),
`refund_account` varchar(140),
`refund_amount` decimal(21,9) not null default 0,
`reference_number` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2024-11-10 20:27:01,651 WARNING database DDL Query made to DB:
create table `tabLoan Application` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`applicant_type` varchar(140),
`applicant` varchar(140),
`applicant_name` varchar(140),
`company` varchar(140),
`posting_date` date,
`status` varchar(140),
`loan_product` varchar(140),
`is_term_loan` int(1) not null default 0,
`loan_amount` decimal(21,9) not null default 0,
`is_secured_loan` int(1) not null default 0,
`rate_of_interest` decimal(21,9) not null default 0,
`description` text,
`maximum_loan_amount` decimal(21,9) not null default 0,
`repayment_method` varchar(140),
`total_payable_amount` decimal(21,9) not null default 0,
`repayment_periods` int(11) not null default 0,
`repayment_amount` decimal(21,9) not null default 0,
`total_payable_interest` decimal(21,9) not null default 0,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2024-11-10 20:27:01,728 WARNING database DDL Query made to DB:
create table `tabProcess Loan Security Shortfall` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`update_time` datetime(6),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2024-11-10 20:27:01,823 WARNING database DDL Query made to DB:
create table `tabLoan Balance Adjustment` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`loan` varchar(140),
`applicant_type` varchar(140),
`applicant` varchar(140),
`company` varchar(140),
`posting_date` date,
`cost_center` varchar(140),
`adjustment_account` varchar(140),
`adjustment_receivable_account` varchar(140),
`reference_document_type` varchar(140),
`reference_name` varchar(140),
`adjustment_type` varchar(140),
`amount` decimal(21,9) not null default 0,
`reference_number` varchar(140),
`remarks` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2024-11-10 20:27:01,908 WARNING database DDL Query made to DB:
create table `tabRepayment Schedule` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`payment_date` date,
`number_of_days` int(11) not null default 0,
`principal_amount` decimal(21,9) not null default 0,
`interest_amount` decimal(21,9) not null default 0,
`total_payment` decimal(21,9) not null default 0,
`balance_loan_amount` decimal(21,9) not null default 0,
`is_accrued` int(1) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2024-11-10 20:27:01,983 WARNING database DDL Query made to DB:
create table `tabSanctioned Loan Amount` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`applicant_type` varchar(140),
`applicant` varchar(140),
`company` varchar(140),
`sanctioned_amount_limit` decimal(21,9) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2024-11-10 20:27:02,069 WARNING database DDL Query made to DB:
create table `tabProposed Pledge` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`loan_security` varchar(140),
`loan_security_name` varchar(140),
`qty` decimal(21,9) not null default 0,
`loan_security_price` decimal(21,9) not null default 0,
`amount` decimal(21,9) not null default 0,
`haircut` decimal(21,9) not null default 0,
`post_haircut_amount` decimal(21,9) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2024-11-10 20:27:02,143 WARNING database DDL Query made to DB:
create table `tabUnpledge` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`loan_security` varchar(140),
`loan_security_name` varchar(140),
`loan_security_type` varchar(140),
`loan_security_code` varchar(140),
`haircut` decimal(21,9) not null default 0,
`uom` varchar(140),
`qty` decimal(21,9) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2024-11-10 20:27:02,251 WARNING database DDL Query made to DB:
create table `tabLoan Repayment` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`against_loan` varchar(140),
`applicant_type` varchar(140),
`applicant` varchar(140),
`loan_product` varchar(140),
`repayment_type` varchar(140) default 'Normal Repayment',
`loan_restructure` varchar(140),
`company` varchar(140),
`posting_date` datetime(6),
`clearance_date` date,
`rate_of_interest` decimal(21,9) not null default 0,
`days_past_due` int(11) not null default 0,
`is_term_loan` int(1) not null default 0,
`offset_based_on_npa` int(1) not null default 0,
`is_npa` int(1) not null default 0,
`manual_npa` int(1) not null default 0,
`due_date` date,
`pending_principal_amount` decimal(21,9) not null default 0,
`interest_payable` decimal(21,9) not null default 0,
`payable_amount` decimal(21,9) not null default 0,
`shortfall_amount` decimal(21,9) not null default 0,
`payable_principal_amount` decimal(21,9) not null default 0,
`penalty_amount` decimal(21,9) not null default 0,
`amount_paid` decimal(21,9) not null default 0,
`cost_center` varchar(140),
`total_charges_payable` decimal(21,9) not null default 0,
`total_paid_charges` decimal(21,9) not null default 0,
`reference_number` varchar(140),
`reference_date` date,
`principal_amount_paid` decimal(21,9) not null default 0,
`total_penalty_paid` decimal(21,9) not null default 0,
`total_interest_paid` decimal(21,9) not null default 0,
`amended_from` varchar(140),
`payment_account` varchar(140),
`penalty_income_account` varchar(140),
`loan_account` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2024-11-10 20:27:02,330 WARNING database DDL Query made to DB:
create table `tabLoan Security Deposit` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`loan` varchar(140),
`loan_disbursement` varchar(140),
`deposit_amount` decimal(21,9) not null default 0,
`allocated_amount` decimal(21,9) not null default 0,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2024-11-10 20:27:02,467 WARNING database DDL Query made to DB:
create table `tabLoan Product` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`product_code` varchar(140) unique,
`product_name` varchar(140) unique,
`maximum_loan_amount` decimal(21,9) not null default 0,
`rate_of_interest` decimal(21,9) not null default 0,
`penalty_interest_method` varchar(140),
`penalty_interest_rate` decimal(21,9) not null default 0,
`penalty_interest_value_ptpd` decimal(21,9) not null default 0,
`grace_period_in_days` int(11) not null default 0,
`write_off_amount` decimal(21,9) not null default 0,
`min_days_bw_disbursement_first_repayment` int(11) not null default 0,
`broken_period_interest_charged` int(1) not null default 0,
`auto_close_with_security_deposit` int(1) not null default 0,
`min_auto_closure_tolerance_amount` decimal(21,9) not null default 0,
`max_auto_closure_tolerance_amount` decimal(21,9) not null default 0,
`company` varchar(140),
`is_term_loan` int(1) not null default 0,
`disabled` int(1) not null default 0,
`repayment_schedule_type` varchar(140),
`cyclic_day_of_the_month` varchar(140),
`repayment_date_on` varchar(140),
`days_past_due_threshold_for_npa` int(11) not null default 0,
`description` text,
`mode_of_payment` varchar(140),
`disbursement_account` varchar(140),
`payment_account` varchar(140),
`loan_account` varchar(140),
`security_deposit_account` varchar(140),
`suspense_collection_account` varchar(140),
`interest_income_account` varchar(140),
`interest_accrued_account` varchar(140),
`interest_waiver_account` varchar(140),
`broken_period_interest_recovery_account` varchar(140),
`interest_receivable_account` varchar(140),
`suspense_interest_income` varchar(140),
`suspense_interest_receivable` varchar(140),
`same_as_regular_interest_accounts` int(1) not null default 0,
`additional_interest_income` varchar(140),
`additional_interest_accrued` varchar(140),
`additional_interest_receivable` varchar(140),
`additional_interest_suspense` varchar(140),
`additional_interest_waiver` varchar(140),
`penalty_income_account` varchar(140),
`penalty_accrued_account` varchar(140),
`penalty_waiver_account` varchar(140),
`penalty_receivable_account` varchar(140),
`penalty_suspense_account` varchar(140),
`write_off_account` varchar(140),
`write_off_recovery_account` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2024-11-10 20:27:02,548 WARNING database DDL Query made to DB:
create table `tabLoan Security Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`loan_security_type` varchar(140) unique,
`unit_of_measure` varchar(140),
`haircut` decimal(21,9) not null default 0,
`loan_to_value_ratio` decimal(21,9) not null default 0,
`disabled` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2024-11-10 20:27:02,625 WARNING database DDL Query made to DB:
create table `tabLoan IRAC Provisioning Configuration` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`classification_code` varchar(140),
`classification_name` varchar(140),
`security_type` varchar(140),
`provision_rate` decimal(21,9) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2024-11-10 20:27:02,731 WARNING database DDL Query made to DB:
create table `tabLoan Partner` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`partner_code` varchar(140) unique,
`partner_name` varchar(140) unique,
`partner_loan_share_percentage` decimal(21,9) not null default 0,
`partner_base_interest_rate` decimal(21,9) not null default 0,
`effective_date` date,
`repayment_schedule_type` varchar(140),
`company_loan_share_percentage` decimal(21,9) not null default 0,
`company_base_interest_rate` decimal(21,9) not null default 0,
`interest_increment_percentage` decimal(21,9) not null default 0,
`incremental_interest_applicable` int(1) not null default 0,
`organization_type` varchar(140),
`primary_address` varchar(140),
`fldg_trigger_dpd` int(11) not null default 0,
`fldg_limit_calculation_component` varchar(140),
`type_of_fldg_applicable` varchar(140),
`fldg_fixed_deposit_percentage` decimal(21,9) not null default 0,
`fldg_corporate_guarantee_percentage` decimal(21,9) not null default 0,
`servicer_fee` int(1) not null default 0,
`restructure_of_loans_applicable` int(1) not null default 0,
`waiving_of_charges_applicable` int(1) not null default 0,
`partial_payment_mechanism` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2024-11-10 20:27:02,812 WARNING database DDL Query made to DB:
create table `tabDays Past Due Log` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`loan` varchar(140),
`posting_date` date,
`days_past_due` int(11) not null default 0,
`process_loan_classification` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2024-11-10 20:27:02,892 WARNING database DDL Query made to DB:
create table `tabProcess Loan Classification` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`posting_date` date,
`loan_product` varchar(140),
`loan` varchar(140),
`previous_process` varchar(140),
`payment_reference` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2024-11-10 20:27:02,971 WARNING database DDL Query made to DB:
create table `tabLoan Disbursement Charge` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`charge` varchar(140),
`amount` decimal(21,9) not null default 0,
`account` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2024-11-10 20:27:03,048 WARNING database DDL Query made to DB:
create table `tabPledge` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`loan_security` varchar(140),
`loan_security_name` varchar(140),
`loan_security_type` varchar(140),
`loan_security_code` varchar(140),
`uom` varchar(140),
`qty` decimal(21,9) not null default 0,
`haircut` decimal(21,9) not null default 0,
`loan_security_price` decimal(21,9) not null default 0,
`amount` decimal(21,9) not null default 0,
`post_haircut_amount` decimal(21,9) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2024-11-10 20:27:03,133 WARNING database DDL Query made to DB:
create table `tabLoan Security Unpledge` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`loan` varchar(140),
`applicant_type` varchar(140),
`applicant` varchar(140),
`company` varchar(140),
`unpledge_time` datetime(6),
`status` varchar(140) default 'Requested',
`reference_no` varchar(140),
`description` text,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2024-11-10 20:27:03,221 WARNING database DDL Query made to DB:
create table `tabLoan Security Price` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`loan_security` varchar(140),
`loan_security_name` varchar(140),
`loan_security_type` varchar(140),
`uom` varchar(140),
`loan_security_price` decimal(21,9) not null default 0,
`valid_from` datetime(6),
`valid_upto` datetime(6),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2024-11-10 20:27:03,305 WARNING database DDL Query made to DB:
create table `tabLoan Security` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`loan_security_name` varchar(140) unique,
`haircut` decimal(21,9) not null default 0,
`loan_security_code` varchar(140) unique,
`loan_security_type` varchar(140),
`unit_of_measure` varchar(140),
`disabled` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2024-11-10 20:27:03,398 WARNING database DDL Query made to DB:
create table `tabLoan Security Shortfall` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`loan` varchar(140),
`applicant_type` varchar(140),
`applicant` varchar(140),
`status` varchar(140) default 'Pending',
`shortfall_time` datetime(6),
`loan_amount` decimal(21,9) not null default 0,
`shortfall_amount` decimal(21,9) not null default 0,
`security_value` decimal(21,9) not null default 0,
`shortfall_percentage` decimal(21,9) not null default 0,
`process_loan_security_shortfall` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2024-11-10 20:27:03,530 WARNING database DDL Query made to DB:
create table `tabLoan Restructure` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`loan` varchar(140),
`applicant_type` varchar(140),
`applicant` varchar(140),
`repayment_method` varchar(140),
`restructure_type` varchar(140),
`branch` varchar(140),
`status` varchar(140),
`total_amount_paid` decimal(21,9) not null default 0,
`total_principal_paid` decimal(21,9) not null default 0,
`company` varchar(140),
`restructure_date` date,
`reason_for_restructure` text,
`loan_product` varchar(140),
`old_tenure` int(11) not null default 0,
`completed_tenure` int(11) not null default 0,
`old_rate_of_interest` decimal(21,9) not null default 0,
`old_loan_amount` decimal(21,9) not null default 0,
`old_emi` decimal(21,9) not null default 0,
`current_restructure_count` int(11) not null default 0,
`pre_restructure_dpd` int(11) not null default 0,
`disbursed_amount` decimal(21,9) not null default 0,
`total_overdue_amount` decimal(21,9) not null default 0,
`restructure_charges` decimal(21,9) not null default 0,
`waive_off_restructure_charges` int(1) not null default 0,
`pending_principal_amount` decimal(21,9) not null default 0,
`available_security_deposit` decimal(21,9) not null default 0,
`principal_overdue` decimal(21,9) not null default 0,
`principal_adjusted` decimal(21,9) not null default 0,
`balance_principal` decimal(21,9) not null default 0,
`interest_overdue` decimal(21,9) not null default 0,
`unaccrued_interest` decimal(21,9) not null default 0,
`penalty_overdue` decimal(21,9) not null default 0,
`charges_overdue` decimal(21,9) not null default 0,
`adjusted_interest_amount` decimal(21,9) not null default 0,
`adjusted_unaccrued_interest` decimal(21,9) not null default 0,
`interest_waiver_amount` decimal(21,9) not null default 0,
`unaccrued_interest_waiver` decimal(21,9) not null default 0,
`penal_interest_waiver` decimal(21,9) not null default 0,
`other_charges_waiver` decimal(21,9) not null default 0,
`balance_interest_amount` decimal(21,9) not null default 0,
`balance_unaccrued_interest` decimal(21,9) not null default 0,
`balance_penalty_amount` decimal(21,9) not null default 0,
`balance_charges` decimal(21,9) not null default 0,
`treatment_of_normal_interest` varchar(140) default 'Add To First EMI',
`unaccrued_interest_treatment` varchar(140) default 'Add To First EMI',
`treatment_of_penal_interest` varchar(140) default 'Capitalize',
`treatment_of_other_charges` varchar(140) default 'Capitalize',
`new_rate_of_interest` decimal(21,9) not null default 0,
`repayment_start_date` date,
`new_repayment_method` varchar(140),
`new_repayment_period_in_months` int(11) not null default 0,
`new_monthly_repayment_amount` decimal(21,9) not null default 0,
`new_loan_amount` decimal(21,9) not null default 0,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2024-11-10 20:27:03,613 WARNING database DDL Query made to DB:
create table `tabLoan Repayment Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`loan_interest_accrual` varchar(140),
`paid_principal_amount` decimal(21,9) not null default 0,
`paid_interest_amount` decimal(21,9) not null default 0,
`accrual_type` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2024-11-10 20:27:03,694 WARNING database DDL Query made to DB:
create table `tabLoan Charges` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`charge_type` varchar(140),
`charge_based_on` varchar(140),
`percentage` decimal(21,9) not null default 0,
`amount` decimal(21,9) not null default 0,
`event` varchar(140),
`income_account` varchar(140),
`waiver_account` varchar(140),
`suspense_account` varchar(140),
`receivable_account` varchar(140),
`write_off_account` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2024-11-10 20:27:04,839 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice` ADD COLUMN `loan` varchar(140)
2024-11-10 20:27:04,856 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice` MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `base_write_off_amount` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `write_off_amount` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `base_paid_amount` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `base_change_amount` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `total_advance` decimal(21,9) not null default 0, MODIFY `total_billing_hours` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `amount_eligible_for_commission` decimal(21,9) not null default 0, MODIFY `outstanding_amount` decimal(21,9) not null default 0, MODIFY `change_amount` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `commission_rate` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `loyalty_amount` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `total_commission` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0
2024-11-10 20:27:04,908 WARNING database DDL Query made to DB:
ALTER TABLE `tabItem Default` ADD COLUMN `default_receivable_account` varchar(140), ADD COLUMN `default_waiver_account` varchar(140), ADD COLUMN `default_write_off_account` varchar(140), ADD COLUMN `default_suspense_account` varchar(140)
2024-11-10 20:27:04,972 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompany` ADD COLUMN `loan_restructure_limit` decimal(21,9) not null default 0, ADD COLUMN `watch_period_post_loan_restructure_in_days` int(11) not null default 0, ADD COLUMN `interest_day_count_convention` varchar(140), ADD COLUMN `min_days_bw_disbursement_first_repayment` int(11) not null default 0, ADD COLUMN `collection_offset_logic_based_on` varchar(140), ADD COLUMN `days_past_due_threshold` int(11) not null default 0, ADD COLUMN `collection_offset_sequence_for_sub_standard_asset` varchar(140), ADD COLUMN `collection_offset_sequence_for_standard_asset` varchar(140), ADD COLUMN `collection_offset_sequence_for_written_off_asset` varchar(140), ADD COLUMN `collection_offset_sequence_for_settlement_collection` varchar(140)
2024-11-10 20:27:04,989 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompany` MODIFY `total_monthly_sales` decimal(21,9) not null default 0, MODIFY `credit_limit` decimal(21,9) not null default 0, MODIFY `monthly_sales_target` decimal(21,9) not null default 0
2024-11-10 20:27:05,047 WARNING database DDL Query made to DB:
ALTER TABLE `tabCustomer` ADD COLUMN `is_npa` int(1) not null default 0
2024-11-10 20:27:05,063 WARNING database DDL Query made to DB:
ALTER TABLE `tabCustomer` MODIFY `default_commission_rate` decimal(21,9) not null default 0
2024-11-10 20:27:05,229 WARNING database DDL Query made to DB:
ALTER TABLE `tabLoan` ADD COLUMN `repay_from_salary` int(1) not null default 0
2024-11-10 20:27:05,244 WARNING database DDL Query made to DB:
ALTER TABLE `tabLoan` MODIFY `rate_of_interest` decimal(21,9) not null default 0, MODIFY `disbursed_amount` decimal(21,9) not null default 0, MODIFY `refund_amount` decimal(21,9) not null default 0, MODIFY `credit_adjustment_amount` decimal(21,9) not null default 0, MODIFY `total_amount_paid` decimal(21,9) not null default 0, MODIFY `total_principal_paid` decimal(21,9) not null default 0, MODIFY `loan_amount` decimal(21,9) not null default 0, MODIFY `debit_adjustment_amount` decimal(21,9) not null default 0, MODIFY `maximum_loan_amount` decimal(21,9) not null default 0, MODIFY `monthly_repayment_amount` decimal(21,9) not null default 0, MODIFY `written_off_amount` decimal(21,9) not null default 0
2024-11-10 20:27:05,266 WARNING database DDL Query made to DB:
ALTER TABLE `tabLoan` ADD INDEX `creation`(`creation`)
2024-11-10 20:27:05,319 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip` ADD COLUMN `total_principal_amount` decimal(21,9) not null default 0, ADD COLUMN `total_interest_amount` decimal(21,9) not null default 0, ADD COLUMN `total_loan_repayment` decimal(21,9) not null default 0
2024-11-10 20:27:05,335 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip` MODIFY `total_deduction` decimal(21,9) not null default 0, MODIFY `base_total_deduction` decimal(21,9) not null default 0, MODIFY `net_pay` decimal(21,9) not null default 0, MODIFY `base_net_pay` decimal(21,9) not null default 0, MODIFY `total_working_days` decimal(21,9) not null default 0, MODIFY `unmarked_days` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `leave_without_pay` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `absent_days` decimal(21,9) not null default 0, MODIFY `year_to_date` decimal(21,9) not null default 0, MODIFY `base_year_to_date` decimal(21,9) not null default 0, MODIFY `month_to_date` decimal(21,9) not null default 0, MODIFY `base_month_to_date` decimal(21,9) not null default 0, MODIFY `ctc` decimal(21,9) not null default 0, MODIFY `income_from_other_sources` decimal(21,9) not null default 0, MODIFY `total_earnings` decimal(21,9) not null default 0, MODIFY `non_taxable_earnings` decimal(21,9) not null default 0, MODIFY `standard_tax_exemption_amount` decimal(21,9) not null default 0, MODIFY `tax_exemption_declaration` decimal(21,9) not null default 0, MODIFY `deductions_before_tax_calculation` decimal(21,9) not null default 0, MODIFY `annual_taxable_amount` decimal(21,9) not null default 0, MODIFY `income_tax_deducted_till_date` decimal(21,9) not null default 0, MODIFY `payment_days` decimal(21,9) not null default 0, MODIFY `current_month_income_tax` decimal(21,9) not null default 0, MODIFY `total_working_hours` decimal(21,9) not null default 0, MODIFY `future_income_tax_deductions` decimal(21,9) not null default 0, MODIFY `hour_rate` decimal(21,9) not null default 0, MODIFY `total_income_tax` decimal(21,9) not null default 0, MODIFY `base_hour_rate` decimal(21,9) not null default 0, MODIFY `gross_pay` decimal(21,9) not null default 0, MODIFY `base_gross_pay` decimal(21,9) not null default 0, MODIFY `gross_year_to_date` decimal(21,9) not null default 0, MODIFY `base_gross_year_to_date` decimal(21,9) not null default 0
2024-11-10 20:27:05,377 WARNING database DDL Query made to DB:
ALTER TABLE `tabLoan Repayment` ADD COLUMN `repay_from_salary` int(1) not null default 0, ADD COLUMN `process_payroll_accounting_entry_based_on_employee` int(1) not null default 0, ADD COLUMN `payroll_payable_account` varchar(140)
2024-11-10 20:27:05,393 WARNING database DDL Query made to DB:
ALTER TABLE `tabLoan Repayment` MODIFY `penalty_amount` decimal(21,9) not null default 0, MODIFY `total_penalty_paid` decimal(21,9) not null default 0, MODIFY `amount_paid` decimal(21,9) not null default 0, MODIFY `total_interest_paid` decimal(21,9) not null default 0, MODIFY `pending_principal_amount` decimal(21,9) not null default 0, MODIFY `total_charges_payable` decimal(21,9) not null default 0, MODIFY `interest_payable` decimal(21,9) not null default 0, MODIFY `total_paid_charges` decimal(21,9) not null default 0, MODIFY `payable_amount` decimal(21,9) not null default 0, MODIFY `shortfall_amount` decimal(21,9) not null default 0, MODIFY `rate_of_interest` decimal(21,9) not null default 0, MODIFY `payable_principal_amount` decimal(21,9) not null default 0
2024-11-13 10:14:46,305 WARNING database DDL Query made to DB:
create table `tabHD Article` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140),
`category` varchar(140),
`status` varchar(140) default 'Draft',
`published_on` datetime(6),
`author` varchar(140),
`subtitle` text,
`article_image` text,
`content` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2024-11-13 10:14:46,698 WARNING database DDL Query made to DB:
create table `tabHD Synonyms` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`word` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2024-11-13 10:14:46,823 WARNING database DDL Query made to DB:
create table `tabHD Customer` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`image` text,
`customer_name` varchar(140) unique,
`domain` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2024-11-13 10:14:46,895 WARNING database DDL Query made to DB:
create sequence if not exists hd_team_item_id_seq nocache nocycle
2024-11-13 10:14:46,918 WARNING database DDL Query made to DB:
create table `tabHD Team Item` (
			name bigint primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`team` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2024-11-13 10:14:47,047 WARNING database DDL Query made to DB:
create table `tabHD Holiday` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`holiday_date` date,
`weekly_off` int(1) not null default 0,
`description` longtext,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2024-11-13 10:14:47,152 WARNING database DDL Query made to DB:
create table `tabHD Ticket Template` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`template_name` varchar(140) unique,
`about` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2024-11-13 10:14:47,287 WARNING database DDL Query made to DB:
create table `tabHD Article Category` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`category_name` varchar(140),
`icon` varchar(140),
`description` text,
`status` varchar(140) default 'Published',
`lft` int(11) not null default 0,
`rgt` int(11) not null default 0,
`is_group` int(1) not null default 1,
`old_parent` varchar(140),
`parent_category` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2024-11-13 10:14:47,373 WARNING database DDL Query made to DB:
create table `tabHD Article Feedback` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`article` varchar(140),
`user` varchar(140),
`feedback` int(11) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2024-11-13 10:14:47,508 WARNING database DDL Query made to DB:
create table `tabHD Service Holiday List` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`holiday_list_name` varchar(140) unique,
`from_date` date,
`to_date` date,
`total_holidays` int(11) not null default 0,
`weekly_off` varchar(140),
`color` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2024-11-13 10:14:47,602 WARNING database DDL Query made to DB:
create table `tabHD Agent` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`user` varchar(140),
`agent_name` varchar(140),
`user_image` varchar(140),
`is_active` int(1) not null default 1,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2024-11-13 10:14:47,909 WARNING database DDL Query made to DB:
create sequence if not exists hd_ticket_id_seq nocache nocycle
2024-11-13 10:14:47,937 WARNING database DDL Query made to DB:
create table `tabHD Ticket` (
			name bigint primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`subject` varchar(140),
`raised_by` varchar(140),
`status` varchar(140) default 'Open',
`priority` varchar(140),
`ticket_type` varchar(140),
`agent_group` varchar(140),
`ticket_split_from` varchar(140),
`description` longtext,
`template` varchar(140),
`sla` varchar(140),
`response_by` datetime(6),
`agreement_status` varchar(140),
`resolution_by` datetime(6),
`service_level_agreement_creation` datetime(6),
`on_hold_since` datetime(6),
`total_hold_time` decimal(21,9),
`first_response_time` decimal(21,9),
`first_responded_on` datetime(6),
`avg_response_time` decimal(21,9),
`resolution_details` longtext,
`opening_date` date,
`opening_time` time(6),
`resolution_date` datetime(6),
`resolution_time` decimal(21,9),
`user_resolution_time` decimal(21,9),
`contact` varchar(140),
`customer` varchar(140),
`email_account` varchar(140),
`via_customer_portal` int(1) not null default 0,
`attachment` text,
`content_type` varchar(140),
`feedback_rating` decimal(3,2),
`feedback_text` varchar(140),
`feedback` varchar(140),
`feedback_extra` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
`_seen` text,
index `status`(`status`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2024-11-13 10:14:48,061 WARNING database DDL Query made to DB:
create sequence if not exists hd_preset_filter_item_id_seq nocache nocycle
2024-11-13 10:14:48,080 WARNING database DDL Query made to DB:
create table `tabHD Preset Filter Item` (
			name bigint primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`label` varchar(140),
`fieldname` varchar(140),
`filter_type` varchar(140),
`value` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2024-11-13 10:14:48,161 WARNING database DDL Query made to DB:
create table `tabHD Service Level Priority` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`default_priority` int(1) not null default 0,
`priority` varchar(140),
`response_time` decimal(21,9),
`resolution_time` decimal(21,9),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2024-11-13 10:14:48,242 WARNING database DDL Query made to DB:
create table `tabHD Preset Filter` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140),
`reference_doctype` varchar(140),
`type` varchar(140),
`user` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2024-11-13 10:14:48,320 WARNING database DDL Query made to DB:
create table `tabHD Canned Response` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140),
`message` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2024-11-13 10:14:48,433 WARNING database DDL Query made to DB:
create sequence if not exists hd_team_member_id_seq nocache nocycle
2024-11-13 10:14:48,457 WARNING database DDL Query made to DB:
create table `tabHD Team Member` (
			name bigint primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`user` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2024-11-13 10:14:48,579 WARNING database DDL Query made to DB:
create table `tabHD Notification` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`read` int(1) not null default 0,
`user_from` varchar(140),
`user_to` varchar(140),
`notification_type` varchar(140),
`reference_ticket` varchar(140),
`reference_comment` varchar(140),
`message` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2024-11-13 10:14:48,660 WARNING database DDL Query made to DB:
create table `tabHD Ticket Priority` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`integer_value` int(11) not null default 0,
`description` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2024-11-13 10:14:48,794 WARNING database DDL Query made to DB:
create table `tabHD Organization` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`organization_name` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2024-11-13 10:14:48,864 WARNING database DDL Query made to DB:
create table `tabHD Synonym` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`synonym` varchar(140) unique,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2024-11-13 10:14:48,941 WARNING database DDL Query made to DB:
create table `tabHD Team` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`team_name` varchar(140) unique,
`assignment_rule` varchar(140),
`ignore_restrictions` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2024-11-13 10:14:49,016 WARNING database DDL Query made to DB:
create table `tabHD Ticket Activity` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`ticket` varchar(140),
`action` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2024-11-13 10:14:49,153 WARNING database DDL Query made to DB:
create table `tabHD Escalation Rule` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`is_enabled` int(1) not null default 0,
`priority` varchar(140),
`team` varchar(140),
`ticket_type` varchar(140),
`to_agent` varchar(140),
`to_team` varchar(140),
`to_priority` varchar(140),
`to_ticket_type` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2024-11-13 10:14:49,249 WARNING database DDL Query made to DB:
create table `tabHD Article Sub Category Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`sub_category` varchar(140) unique,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2024-11-13 10:14:49,325 WARNING database DDL Query made to DB:
create table `tabHD Service Day` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`workday` varchar(140),
`start_time` time(6),
`end_time` time(6),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2024-11-13 10:14:49,396 WARNING database DDL Query made to DB:
create table `tabHD Ticket Feedback Option` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`rating` decimal(3,2),
`label` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2024-11-13 10:14:49,477 WARNING database DDL Query made to DB:
create table `tabHD Desk Account Request` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`request_key` varchar(140),
`ip_address` varchar(140),
`email` varchar(140) unique,
`user` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2024-11-13 10:14:49,550 WARNING database DDL Query made to DB:
create table `tabHD Ticket Template Field` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`fieldname` varchar(140),
`url_method` varchar(140),
`required` int(1) not null default 0,
`hide_from_customer` int(1) not null default 0,
`placeholder` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2024-11-13 10:14:49,618 WARNING database DDL Query made to DB:
create table `tabHD Portal Signup Request` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`request_key` varchar(140),
`user` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2024-11-13 10:14:49,747 WARNING database DDL Query made to DB:
create table `tabHD Ticket Comment` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`reference_ticket` varchar(140),
`commented_by` varchar(140),
`is_pinned` int(1) not null default 0,
`content` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `reference_ticket`(`reference_ticket`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2024-11-13 10:14:49,877 WARNING database DDL Query made to DB:
create table `tabHD Pause Service Level Agreement On Status` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`status` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2024-11-13 10:14:49,942 WARNING database DDL Query made to DB:
create table `tabHD Organization Contact Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`contact` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2024-11-13 10:14:50,009 WARNING database DDL Query made to DB:
create table `tabHD Article Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`article` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2024-11-13 10:14:50,080 WARNING database DDL Query made to DB:
create table `tabHD Ticket Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`is_system` int(1) not null default 0,
`description` text,
`priority` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2024-11-13 10:14:50,167 WARNING database DDL Query made to DB:
create table `tabHD Form Script` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`dt` varchar(140),
`apply_to` varchar(140) default 'Form',
`enabled` int(1) not null default 0,
`script` longtext default 'function setupForm({ doc }) {\n    return {\n        actions: [],\n    }\n}',
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2024-11-13 10:14:50,260 WARNING database DDL Query made to DB:
create table `tabHD Service Level Agreement` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`default_priority` varchar(140),
`service_level` varchar(140) unique,
`enabled` int(1) not null default 1,
`default_sla` int(1) not null default 0,
`condition` longtext,
`start_date` date,
`end_date` date,
`apply_sla_for_resolution` int(1) not null default 1,
`holiday_list` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2024-11-13 10:14:50,416 WARNING database DDL Query made to DB:
create table `tabHD Action` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`is_enabled` int(1) not null default 0,
`button_label` varchar(140),
`button_icon` varchar(140),
`is_external_link` int(1) not null default 0,
`action` longtext,
`cond_hidden` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2024-11-13 10:14:50,533 WARNING database DDL Query made to DB:
create table `tabHD Stopword` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`word` varchar(140) unique,
`enabled` int(1) not null default 1,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2024-11-13 10:14:50,642 WARNING database DDL Query made to DB:
create table `tabHD Support Search Source` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`source_name` varchar(140),
`source_type` varchar(140),
`base_url` varchar(140),
`query_route` varchar(140),
`search_term_param_name` varchar(140),
`response_result_key_path` varchar(140),
`post_route` varchar(140),
`post_route_key_list` varchar(140),
`post_title_key` varchar(140),
`post_description_key` varchar(140),
`source_doctype` varchar(140),
`result_title_field` varchar(140),
`result_preview_field` varchar(140),
`result_route_field` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2024-11-13 10:14:50,712 WARNING database DDL Query made to DB:
create table `tabHD Service Level Agreement Fulfilled On Status` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`status` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2024-11-20 17:14:25,375 WARNING database DDL Query made to DB:
create table `tabImport CSV` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2024-11-20 17:15:26,935 WARNING database DDL Query made to DB:
ALTER TABLE `tabImport CSV` ADD COLUMN `import_csv` text
2024-11-20 17:15:51,300 WARNING database DDL Query made to DB:
create table `tabCSV Data` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2024-11-20 17:18:34,069 WARNING database DDL Query made to DB:
alter table `tabCSV Data` add column if not exists parent varchar(140)
2024-11-20 17:18:34,070 WARNING database DDL Query made to DB:
alter table `tabCSV Data` add column if not exists parenttype varchar(140)
2024-11-20 17:18:34,070 WARNING database DDL Query made to DB:
alter table `tabCSV Data` add column if not exists parentfield varchar(140)
2024-11-20 17:18:34,235 WARNING database DDL Query made to DB:
ALTER TABLE `tabCSV Data` ADD COLUMN `employee_name` varchar(140), ADD COLUMN `log_type` varchar(140), ADD COLUMN `time` datetime(6), ADD COLUMN `shift` varchar(140), ADD COLUMN `device_id` varchar(140)
2024-11-20 17:20:20,020 WARNING database DDL Query made to DB:
alter table `tabCSV Data` add column if not exists parent varchar(140)
2024-11-20 17:20:20,020 WARNING database DDL Query made to DB:
alter table `tabCSV Data` add column if not exists parenttype varchar(140)
2024-11-20 17:20:20,021 WARNING database DDL Query made to DB:
alter table `tabCSV Data` add column if not exists parentfield varchar(140)
2024-11-20 17:20:50,732 WARNING database DDL Query made to DB:
alter table `tabCSV Data` add column if not exists parent varchar(140)
2024-11-20 17:20:50,734 WARNING database DDL Query made to DB:
alter table `tabCSV Data` add column if not exists parenttype varchar(140)
2024-11-20 17:20:50,735 WARNING database DDL Query made to DB:
alter table `tabCSV Data` add column if not exists parentfield varchar(140)
2024-11-20 17:35:13,657 WARNING database DDL Query made to DB:
RENAME TABLE `tabCSV Data` TO `tabAttendance Log`
2024-11-20 18:03:26,280 WARNING database DDL Query made to DB:
ALTER TABLE `tabImport CSV` ADD COLUMN `csv_file` text
2024-12-12 23:16:39,267 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2024-12-12 23:16:41,008 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2024-12-12 23:16:42,402 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice` ADD COLUMN `destination` varchar(140)
2024-12-12 23:16:42,420 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice` MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `commission_rate` decimal(21,9) not null default 0, MODIFY `total_advance` decimal(21,9) not null default 0, MODIFY `outstanding_amount` decimal(21,9) not null default 0, MODIFY `amount_eligible_for_commission` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `loyalty_amount` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `base_paid_amount` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `base_write_off_amount` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `write_off_amount` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `total_billing_hours` decimal(21,9) not null default 0, MODIFY `change_amount` decimal(21,9) not null default 0, MODIFY `total_commission` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `base_change_amount` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0
2024-12-12 23:16:44,927 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2024-12-12 23:16:46,551 WARNING database DDL Query made to DB:
ALTER TABLE `tabDelivery Note` ADD COLUMN `destination` varchar(140)
2024-12-12 23:16:46,568 WARNING database DDL Query made to DB:
ALTER TABLE `tabDelivery Note` MODIFY `total` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `per_returned` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `commission_rate` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `per_billed` decimal(21,9) not null default 0, MODIFY `amount_eligible_for_commission` decimal(21,9) not null default 0, MODIFY `total_commission` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `per_installed` decimal(21,9) not null default 0
2024-12-12 23:16:46,958 WARNING database DDL Query made to DB:
ALTER TABLE `tabItem` MODIFY `total_projected_qty` decimal(21,9) not null default 0, MODIFY `over_delivery_receipt_allowance` decimal(21,9) not null default 0, MODIFY `safety_stock` decimal(21,9) not null default 0, MODIFY `last_purchase_rate` decimal(21,9) not null default 0, MODIFY `max_discount` decimal(21,9) not null default 0, MODIFY `over_billing_allowance` decimal(21,9) not null default 0, MODIFY `valuation_rate` decimal(21,9) not null default 0, MODIFY `stock_uom` varchar(140) default 'Nos', MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `standard_rate` decimal(21,9) not null default 0, MODIFY `opening_stock` decimal(21,9) not null default 0, MODIFY `is_stock_item` int(1) not null default 0
2024-12-13 00:22:51,393 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2024-12-13 00:22:52,415 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2024-12-13 00:22:53,492 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2024-12-30 09:25:46,949 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Order` ADD COLUMN `custom_notify_me_on_receipt_of_all` int(1) not null default 0
2024-12-30 09:25:46,968 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Order` MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `taxes_and_charges_added` decimal(21,9) not null default 0, MODIFY `per_billed` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `base_tax_withholding_net_total` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `base_taxes_and_charges_added` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `base_taxes_and_charges_deducted` decimal(21,9) not null default 0, MODIFY `advance_paid` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `taxes_and_charges_deducted` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `per_received` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0
2024-12-30 09:25:47,130 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Order` MODIFY `base_tax_withholding_net_total` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `taxes_and_charges_added` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `taxes_and_charges_deducted` decimal(21,9) not null default 0, MODIFY `per_received` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `base_taxes_and_charges_added` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `base_taxes_and_charges_deducted` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `advance_paid` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `per_billed` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0
2024-12-30 09:26:23,657 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Order`
				CHANGE COLUMN `custom_notify_me_on_receipt_of_all`
				`notify_me_on_receipt_of_all`
				int(1)
2025-01-02 16:00:20,087 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Application` ADD COLUMN `workflow_state` varchar(140)
2025-01-02 16:00:20,112 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Application` MODIFY `total_leave_days` decimal(21,9) not null default 0, MODIFY `leave_balance` decimal(21,9) not null default 0
2025-01-03 10:06:29,168 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-01-03 10:06:29,996 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-01-03 10:06:30,927 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-01-03 10:06:31,770 WARNING database DDL Query made to DB:
create table `tabBiometric Data Staging` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`attendance_device_id` varchar(140),
`timestamp` datetime(6),
`punch_type` varchar(140),
`device_id` varchar(140),
`status` varchar(140) default 'Pending',
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-01-14 11:31:48,924 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmail Template` ADD COLUMN `custom_disabled` int(1) not null default 0
2025-01-14 11:32:12,229 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmail Template`
				CHANGE COLUMN `custom_disabled`
				`disabled`
				int(1)
2025-01-14 11:50:58,817 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmail Template`
				CHANGE COLUMN `disabled`
				`enabled`
				int(1)
2025-01-28 16:40:26,646 WARNING database DDL Query made to DB:
ALTER TABLE `tabMaterial Request` ADD COLUMN `workflow_state` varchar(140)
2025-01-28 16:40:26,658 WARNING database DDL Query made to DB:
ALTER TABLE `tabMaterial Request` MODIFY `per_ordered` decimal(21,9) not null default 0, MODIFY `per_received` decimal(21,9) not null default 0
2025-02-06 23:12:52,132 WARNING database DDL Query made to DB:
ALTER TABLE `tabWhatsApp Message` ADD COLUMN `label` varchar(140), ADD COLUMN `type` varchar(140), ADD COLUMN `from` varchar(140), ADD COLUMN `use_template` int(1) not null default 0, ADD COLUMN `template` varchar(140), ADD COLUMN `template_parameters` text, ADD COLUMN `template_header_parameters` text, ADD COLUMN `message_type` varchar(140), ADD COLUMN `message_id` varchar(140), ADD COLUMN `conversation_id` varchar(140), ADD COLUMN `content_type` varchar(140), ADD COLUMN `attach` text, ADD COLUMN `is_reply` int(1) not null default 0, ADD COLUMN `reply_to_message_id` varchar(140), ADD COLUMN `reference_name` varchar(140), ADD COLUMN `_seen` text
2025-02-06 23:12:52,246 WARNING database DDL Query made to DB:
ALTER TABLE `tabWhatsApp Message`
				ADD INDEX `reference_doctype_reference_name_index`(reference_doctype, reference_name)
2025-02-06 23:12:52,484 WARNING database DDL Query made to DB:
create table `tabWhatsApp Templates` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`template_name` varchar(140) unique,
`actual_name` varchar(140),
`status` varchar(140) default 'Pending',
`id` varchar(140),
`template` longtext,
`sample_values` text,
`for_doctype` varchar(140),
`field_names` text,
`category` varchar(140),
`language` varchar(140),
`language_code` varchar(140),
`header_type` varchar(140),
`header` varchar(140),
`sample` text,
`footer` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-02-06 23:12:52,597 WARNING database DDL Query made to DB:
create table `tabWhatsApp Notification Log` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`template` varchar(140),
`meta_data` json,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-02-06 23:12:52,663 WARNING database DDL Query made to DB:
create sequence if not exists whatsapp_message_fields_id_seq nocache nocycle
2025-02-06 23:12:52,676 WARNING database DDL Query made to DB:
create table `tabWhatsApp Message Fields` (
			name bigint primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`field_name` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-02-06 23:12:52,838 WARNING database DDL Query made to DB:
create table `tabWhatsApp Notification` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`notification_name` varchar(140) unique,
`notification_type` varchar(140),
`reference_doctype` varchar(140),
`field_name` varchar(140),
`event_frequency` varchar(140),
`doctype_event` varchar(140),
`days_in_advance` int(11) not null default 0,
`date_changed` varchar(140),
`disabled` int(1) not null default 0,
`template` varchar(140),
`code` longtext,
`attach_document_print` int(1) not null default 0,
`custom_attachment` int(1) not null default 0,
`attach` text,
`attach_from_field` varchar(140),
`file_name` varchar(140),
`condition` longtext,
`header_type` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-02-06 23:14:04,732 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-02-06 23:14:05,695 WARNING database DDL Query made to DB:
create table `tabIntegration Settings` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`service_name` varchar(140),
`api_key` text,
`endpoint_url` varchar(140),
`status` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-02-06 23:14:05,888 WARNING database DDL Query made to DB:
create table `tabIntegration Log` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`log_id` varchar(140),
`service_name` varchar(140),
`operation` varchar(140),
`status` varchar(140),
`timestamp` datetime(6),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-02-06 23:14:06,022 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-02-06 23:14:06,824 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice` MODIFY `total_billing_hours` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `write_off_amount` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `base_paid_amount` decimal(21,9) not null default 0, MODIFY `commission_rate` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `amount_eligible_for_commission` decimal(21,9) not null default 0, MODIFY `outstanding_amount` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `base_write_off_amount` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `base_change_amount` decimal(21,9) not null default 0, MODIFY `loyalty_amount` decimal(21,9) not null default 0, MODIFY `total_commission` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `total_advance` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `change_amount` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0
2025-02-06 23:14:07,734 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-02-12 13:19:41,935 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-02-12 13:19:42,989 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-02-12 13:19:44,405 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-02-12 13:19:44,938 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee` MODIFY `ctc` decimal(21,9) not null default 0
2025-04-15 17:17:37,099 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabWhatsApp Message Fields`
2025-04-15 17:17:37,216 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabWhatsApp Notification`
2025-04-15 17:17:37,337 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabWhatsApp Message`
2025-04-15 17:17:37,450 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabWhatsApp Templates`
2025-04-15 17:17:37,542 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabWhatsApp Notification Log`
