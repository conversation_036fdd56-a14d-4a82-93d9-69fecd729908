2025-07-25 10:17:34,008 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Format` ADD COLUMN `pdf_generator` varchar(140) default 'wkhtmltopdf'
2025-07-25 10:17:34,331 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrepared Report` ADD COLUMN `peak_memory_usage` int(11) not null default 0
2025-07-25 10:17:34,557 WARNING database DDL Query made to DB:
ALTER TABLE `tabSMS Parameter` MODIFY `value` varchar(255)
2025-07-25 10:17:35,590 WARNING database DDL Query made to DB:
ALTER TABLE `tabScheduled Job Type` ADD COLUMN `scheduler_event` varchar(140)
2025-07-25 10:17:35,880 WARNING database DDL Query made to DB:
ALTER TABLE `tabUser` ADD INDEX `last_active_index`(`last_active`)
2025-07-25 10:17:36,032 WARNING database DDL Query made to DB:
ALTER TABLE `tabNavbar Item` ADD COLUMN `condition` longtext
2025-07-25 10:17:36,214 WARNING database DDL Query made to DB:
ALTER TABLE `tabFile`
				ADD INDEX IF NOT EXISTS `file_url_index`(file_url(100))
2025-07-25 10:17:36,482 WARNING database DDL Query made to DB:
create table `tabScheduler Event` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`scheduled_against` varchar(140),
`method` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-25 10:17:36,784 WARNING database DDL Query made to DB:
ALTER TABLE `tabWeb Page View` ADD INDEX `path_index`(`path`)
2025-07-25 10:17:37,143 WARNING database DDL Query made to DB:
ALTER TABLE `tabWorkflow Transition` ADD COLUMN `send_email_to_creator` int(1) not null default 0
2025-07-25 10:17:37,257 WARNING database DDL Query made to DB:
ALTER TABLE `tabWorkflow Document State` ADD COLUMN `send_email` int(1) not null default 1
2025-07-25 10:17:37,398 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmail Domain` ADD COLUMN `sent_folder_name` varchar(140) default 'Sent'
2025-07-25 10:17:37,645 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmail Account` ADD COLUMN `backend_app_flow` int(1) not null default 0, ADD COLUMN `sent_folder_name` varchar(140), ADD COLUMN `always_bcc` varchar(140)
2025-07-25 10:17:38,559 WARNING database DDL Query made to DB:
ALTER TABLE `tabEvent` MODIFY `google_meet_link` text
2025-07-25 10:17:38,840 WARNING database DDL Query made to DB:
ALTER TABLE `tabTag Link`
				ADD INDEX IF NOT EXISTS `document_type_document_name_index`(document_type, document_name)
2025-07-25 10:17:38,972 WARNING database DDL Query made to DB:
ALTER TABLE `tabNotification Log` MODIFY `link` text
2025-07-25 10:17:39,081 WARNING database DDL Query made to DB:
ALTER TABLE `tabList View Settings` ADD COLUMN `disable_automatic_recency_filters` int(1) not null default 0
2025-07-25 10:17:39,237 WARNING database DDL Query made to DB:
ALTER TABLE `tabWebhook Request Log` MODIFY `url` text
2025-07-25 10:17:39,385 WARNING database DDL Query made to DB:
ALTER TABLE `tabGoogle Calendar` ADD COLUMN `sync_as_public` int(1) not null default 0
2025-07-25 10:17:40,182 WARNING database DDL Query made to DB:
ALTER TABLE `tabHD Article` ADD COLUMN `views` int(11) not null default 0
2025-07-25 10:17:40,414 WARNING database DDL Query made to DB:
ALTER TABLE `tabHD Ticket Template` ADD COLUMN `description_template` longtext
2025-07-25 10:17:40,647 WARNING database DDL Query made to DB:
ALTER TABLE `tabHD Article Feedback` MODIFY `feedback` varchar(140)
2025-07-25 10:17:40,678 WARNING database DDL Query made to DB:
ALTER TABLE `tabHD Article Feedback` ADD INDEX `creation`(`creation`)
